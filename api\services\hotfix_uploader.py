import json
import asyncio
import uuid
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
import hashlib

from api.utils.logger import logger


class HotfixUploaderService:
    """
    Service for simulating document uploads with demo-friendly progress updates.
    Uses JSON file as database and simulates DigitalOcean Spaces uploads.
    """
    
    def __init__(self):
        self.data_dir = Path(__file__).parent.parent / "data"
        self.data_dir.mkdir(exist_ok=True)
        self.jobs_file = self.data_dir / "hotfix_jobs.json"
        self.source_dir = Path(__file__).parent.parent.parent / "mp_materials" / "Investor Relations" / "pdfs"
        
        # Initialize jobs file if it doesn't exist
        if not self.jobs_file.exists():
            self._save_jobs({})
        
        logger.info(f"Initialized HotfixUploaderService with source: {self.source_dir}")
    
    def _load_jobs(self) -> Dict[str, Any]:
        """Load jobs from JSON file"""
        try:
            with open(self.jobs_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def _save_jobs(self, jobs: Dict[str, Any]) -> None:
        """Save jobs to JSON file"""
        try:
            with open(self.jobs_file, 'w', encoding='utf-8') as f:
                json.dump(jobs, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save jobs: {e}")
    
    def get_available_files(self) -> List[Dict[str, Any]]:
        """Get list of available files in the source directory"""
        try:
            files = []
            if not self.source_dir.exists():
                logger.warning(f"Source directory does not exist: {self.source_dir}")
                return files
            
            for file_path in self.source_dir.glob("*.pdf"):
                if file_path.is_file():
                    stat = file_path.stat()
                    files.append({
                        "filename": file_path.name,
                        "file_size": stat.st_size,
                        "modified_at": datetime.fromtimestamp(stat.st_mtime, tz=timezone.utc).isoformat(),
                        "exists": True
                    })
            
            return sorted(files, key=lambda x: x["filename"])
        except Exception as e:
            logger.error(f"Error listing available files: {e}")
            return []
    
    def validate_filenames(self, filenames: List[str]) -> Dict[str, Any]:
        """Validate that requested filenames exist in source directory"""
        available_files = {f["filename"]: f for f in self.get_available_files()}
        
        valid_files = []
        invalid_files = []
        
        for filename in filenames:
            if filename in available_files:
                valid_files.append(available_files[filename])
            else:
                invalid_files.append(filename)
        
        return {
            "valid_files": valid_files,
            "invalid_files": invalid_files,
            "all_valid": len(invalid_files) == 0
        }
    
    def create_upload_job(self, filenames: List[str]) -> Dict[str, Any]:
        """Create a new upload job"""
        # Validate filenames
        validation = self.validate_filenames(filenames)
        
        if not validation["all_valid"]:
            raise ValueError(f"Invalid filenames: {validation['invalid_files']}")
        
        # Generate job ID
        job_id = f"hotfix_{uuid.uuid4().hex[:8]}"
        
        # Create job data
        now = datetime.now(timezone.utc)
        job_data = {
            "job_id": job_id,
            "status": "uploading",
            "total_files": len(validation["valid_files"]),
            "completed_files": 0,
            "failed_files": 0,
            "files": [
                {
                    "filename": file_info["filename"],
                    "status": "pending",
                    "progress": 0,
                    "file_size": file_info["file_size"],
                    "bucket_key": f"uploads/hotfix/{job_id}/{self._safe_filename(file_info['filename'])}",
                    "bucket_url": None,
                    "error_message": None,
                    "started_at": None,
                    "completed_at": None
                }
                for file_info in validation["valid_files"]
            ],
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "estimated_completion": None
        }
        
        # Save job
        jobs = self._load_jobs()
        jobs[job_id] = job_data
        self._save_jobs(jobs)
        
        logger.info(f"Created upload job {job_id} for {len(filenames)} files")
        return job_data
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a job"""
        jobs = self._load_jobs()
        return jobs.get(job_id)
    
    def start_background_upload(self, job_id: str) -> None:
        """Start the background upload simulation"""
        asyncio.create_task(self._simulate_upload_process(job_id))
    
    async def _simulate_upload_process(self, job_id: str) -> None:
        """Simulate the upload process with realistic timing"""
        try:
            logger.info(f"Starting background upload simulation for job {job_id}")
            
            for file_index in range(len(self._get_job_files(job_id))):
                await self._simulate_file_upload(job_id, file_index)
                
            # Mark job as completed
            self._update_job_status(job_id, "completed")
            logger.info(f"Upload simulation completed for job {job_id}")
            
        except Exception as e:
            logger.error(f"Error in upload simulation for job {job_id}: {e}")
            self._update_job_status(job_id, "failed", error=str(e))
    
    async def _simulate_file_upload(self, job_id: str, file_index: int) -> None:
        """Simulate uploading a single file with progress updates"""
        try:
            jobs = self._load_jobs()
            job = jobs.get(job_id)
            if not job:
                return
            
            file_data = job["files"][file_index]
            filename = file_data["filename"]
            
            logger.info(f"Starting upload simulation for {filename}")
            
            # Mark file as uploading
            now = datetime.now(timezone.utc)
            job["files"][file_index].update({
                "status": "uploading",
                "started_at": now.isoformat()
            })
            job["updated_at"] = now.isoformat()
            jobs[job_id] = job
            self._save_jobs(jobs)
            
            # Simulate upload progress in stages
            progress_stages = [25, 50, 75, 100]
            
            for progress in progress_stages:
                await asyncio.sleep(4)  # 4-second intervals as requested
                
                # Update progress
                jobs = self._load_jobs()  # Reload to get latest state
                job = jobs[job_id]
                now = datetime.now(timezone.utc)
                
                job["files"][file_index]["progress"] = progress
                job["updated_at"] = now.isoformat()
                
                if progress == 100:
                    # Mark as completed
                    bucket_url = f"https://quantera.sgp1.digitaloceanspaces.com/{file_data['bucket_key']}"
                    job["files"][file_index].update({
                        "status": "completed",
                        "bucket_url": bucket_url,
                        "completed_at": now.isoformat()
                    })
                    job["completed_files"] += 1
                    logger.info(f"Completed upload simulation for {filename}")
                
                jobs[job_id] = job
                self._save_jobs(jobs)
                
                logger.info(f"Upload progress for {filename}: {progress}%")
                
        except Exception as e:
            logger.error(f"Error simulating upload for file {file_index} in job {job_id}: {e}")
            # Mark file as failed
            jobs = self._load_jobs()
            if job_id in jobs:
                job = jobs[job_id]
                job["files"][file_index].update({
                    "status": "failed",
                    "error_message": str(e)
                })
                job["failed_files"] += 1
                job["updated_at"] = datetime.now(timezone.utc).isoformat()
                jobs[job_id] = job
                self._save_jobs(jobs)
    
    def _get_job_files(self, job_id: str) -> List[Dict[str, Any]]:
        """Get files for a job"""
        jobs = self._load_jobs()
        job = jobs.get(job_id, {})
        return job.get("files", [])
    
    def _update_job_status(self, job_id: str, status: str, error: Optional[str] = None) -> None:
        """Update job status"""
        jobs = self._load_jobs()
        if job_id in jobs:
            job = jobs[job_id]
            job["status"] = status
            job["updated_at"] = datetime.now(timezone.utc).isoformat()
            if error:
                job["error_message"] = error
            jobs[job_id] = job
            self._save_jobs(jobs)
    
    def _safe_filename(self, filename: str) -> str:
        """Generate safe filename for bucket storage"""
        # Remove unsafe characters and replace spaces
        safe = "".join(c for c in filename if c.isalnum() or c in ".-_")
        return safe.replace(" ", "_")
    
    def cleanup_old_jobs(self, days_old: int = 1) -> int:
        """Clean up jobs older than specified days"""
        try:
            jobs = self._load_jobs()
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
            
            jobs_to_remove = []
            for job_id, job_data in jobs.items():
                created_at = datetime.fromisoformat(job_data["created_at"].replace('Z', '+00:00'))
                if created_at < cutoff_date:
                    jobs_to_remove.append(job_id)
            
            for job_id in jobs_to_remove:
                del jobs[job_id]
            
            self._save_jobs(jobs)
            logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")
            return len(jobs_to_remove)
            
        except Exception as e:
            logger.error(f"Error cleaning up old jobs: {e}")
            return 0


# Singleton instance
hotfix_uploader = HotfixUploaderService()