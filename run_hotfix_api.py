#!/usr/bin/env python3
"""
Simple script to run the Hotfix Upload API Server
"""

import subprocess
import sys
from pathlib import Path

def main():
    """Run the hotfix API server"""
    print("🔥 Starting Hotfix Upload API Server...")
    print("=" * 50)
    
    # Check if the files exist
    if not Path("mp_materials/Investor Relations/pdfs").exists():
        print("⚠️  Warning: mp_materials/Investor Relations/pdfs directory not found")
        print("   The API will still work but no files will be available for upload")
        print()
    
    try:
        # Run the hotfix API
        subprocess.run([sys.executable, "hotfix_api.py"], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down Hotfix API Server...")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running hotfix API: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()