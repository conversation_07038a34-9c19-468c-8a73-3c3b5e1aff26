#!/usr/bin/env python3
"""
Standalone Hotfix Upload API Server
A simple demo server for document upload simulation with job polling.
"""

import json
import asyncio
import uuid
import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Import document ingestion pipeline
from agents.rag_agent.document_ingestion import run_batch_ingestion_pipeline

# Import lattice header extraction and retrieval
from agents_v2.latticeheader_agent.base import extract_lattice_headers
from agents.rag_agent.document_retrieval import parallel_lattice_retrieval


# Schemas
class HotfixUploadRequest(BaseModel):
    filenames: List[str] = Field(..., min_items=1, max_items=50)
    
    class Config:
        json_schema_extra = {
            "example": {
                "filenames": ["Annual Report.pdf", "Quarterly Report.pdf", "Current report filing.pdf"]
            }
        }


class FileStatus(BaseModel):
    filename: str
    status: str  # "pending", "uploading", "completed", "failed"
    progress: int = 0  # 0-100
    uploaded_at: Optional[datetime] = None
    error_message: Optional[str] = None


class HotfixJobResponse(BaseModel):
    job_id: str
    status: str  # "uploading", "completed", "failed"
    total_files: int
    completed_files: int
    failed_files: int
    files: List[FileStatus]
    created_at: datetime
    updated_at: datetime
    estimated_completion: Optional[datetime] = None


class HotfixStartResponse(BaseModel):
    job_id: str
    session_id: str  # New session ID
    status: str
    message: str
    total_files: int
    estimated_duration_seconds: int


class FileInfo(BaseModel):
    filename: str
    file_size: int
    last_modified: Optional[datetime] = None


class AvailableFilesResponse(BaseModel):
    files: List[FileInfo]
    total_count: int
    source_directory: str


class ProcessDocsRequest(BaseModel):
    session_id: str
    lattice_headers: Optional[List[str]] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "session_12345",
                "lattice_headers": ["Revenue", "EBITDA", "Cash Flow", "Working Capital"]
            }
        }


class ProcessDocsResponse(BaseModel):
    session_id: str
    status: str
    file_names: List[str]
    total_files: int
    message: str
    lattice_headers: List[str] = []
    header_extraction_time: Optional[float] = None
    header_source: str = "provided"  # "provided" or "extracted"
    lattice_results: Dict[str, Any] = {}  # Retrieval results per header
    retrieval_time: Optional[float] = None
    total_processing_time: Optional[float] = None


# Hotfix Upload Service
class HotfixUploaderService:
    def __init__(self):
        self.data_dir = Path("hotfix_data")
        self.data_dir.mkdir(exist_ok=True)
        self.jobs_file = self.data_dir / "hotfix_jobs.json"
        self.sessions_file = self.data_dir / "sessions.json"
        self.source_dir = Path("mp_materials/pdfs")
        
        # Initialize files if they don't exist
        if not self.jobs_file.exists():
            self._save_jobs({})
        if not self.sessions_file.exists():
            self._save_sessions({})
        
        print(f"✅ Initialized HotfixUploaderService with source: {self.source_dir.absolute()}")

    def _load_jobs(self) -> Dict[str, Any]:
        try:
            with open(self.jobs_file, 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}

    def _save_jobs(self, jobs: Dict[str, Any]) -> None:
        with open(self.jobs_file, 'w') as f:
            json.dump(jobs, f, indent=2, default=str)

    def _load_sessions(self) -> Dict[str, Any]:
        try:
            with open(self.sessions_file, 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}

    def _save_sessions(self, sessions: Dict[str, Any]) -> None:
        with open(self.sessions_file, 'w') as f:
            json.dump(sessions, f, indent=2, default=str)

    def get_available_files(self) -> List[FileInfo]:
        """Get list of available PDF files in the source directory"""
        if not self.source_dir.exists():
            return []
        
        files = []
        for pdf_file in self.source_dir.glob("*.pdf"):
            stat = pdf_file.stat()
            files.append(FileInfo(
                filename=pdf_file.name,
                file_size=stat.st_size,
                last_modified=datetime.fromtimestamp(stat.st_mtime, tz=timezone.utc)
            ))
        
        return sorted(files, key=lambda x: x.filename)

    def validate_filenames(self, filenames: List[str]) -> tuple[List[str], List[str]]:
        """Validate that requested files exist. Returns (valid_files, invalid_files)"""
        available_files = {f.filename for f in self.get_available_files()}
        valid_files = [f for f in filenames if f in available_files]
        invalid_files = [f for f in filenames if f not in available_files]
        return valid_files, invalid_files

    def start_upload_job(self, filenames: List[str]) -> tuple[str, str, HotfixStartResponse]:
        """Start a new upload job with the given filenames. Returns (job_id, session_id, response)"""
        # Validate files
        valid_files, invalid_files = self.validate_filenames(filenames)
        
        if invalid_files:
            raise ValueError(f"Files not found: {invalid_files}")
        
        if not valid_files:
            raise ValueError("No valid files to upload")

        # Generate IDs
        job_id = str(uuid.uuid4())
        session_id = f"session_{str(uuid.uuid4())[:8]}"
        
        # Create file paths mapping
        file_paths = []
        for filename in valid_files:
            file_path = str(self.source_dir / filename)
            file_paths.append(file_path)
        
        # Create session data
        now = datetime.now(timezone.utc)
        session_data = {
            "session_id": session_id,
            "job_id": job_id,
            "filenames": valid_files,
            "file_paths": file_paths,
            "created_at": now.isoformat(),
            "status": "uploading"
        }
        
        # Save session
        sessions = self._load_sessions()
        sessions[session_id] = session_data
        self._save_sessions(sessions)
        
        # Create job data
        job_data = {
            "job_id": job_id,
            "session_id": session_id,
            "status": "uploading",
            "total_files": len(valid_files),
            "completed_files": 0,
            "failed_files": 0,
            "files": [
                {
                    "filename": filename,
                    "status": "pending",
                    "progress": 0,
                    "uploaded_at": None,
                    "error_message": None
                }
                for filename in valid_files
            ],
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "estimated_completion": (now + timedelta(seconds=len(valid_files) * 4)).isoformat()
        }
        
        # Save job
        jobs = self._load_jobs()
        jobs[job_id] = job_data
        self._save_jobs(jobs)
        
        response = HotfixStartResponse(
            job_id=job_id,
            session_id=session_id,
            status="uploading",
            message=f"Started upload job for {len(valid_files)} files",
            total_files=len(valid_files),
            estimated_duration_seconds=len(valid_files) * 4
        )
        
        return job_id, session_id, response

    def get_job_status(self, job_id: str) -> Optional[HotfixJobResponse]:
        """Get current status of a job"""
        jobs = self._load_jobs()
        job_data = jobs.get(job_id)
        
        if not job_data:
            return None
        
        # Convert to response model
        files = [FileStatus(**file_data) for file_data in job_data["files"]]
        
        return HotfixJobResponse(
            job_id=job_data["job_id"],
            status=job_data["status"],
            total_files=job_data["total_files"],
            completed_files=job_data["completed_files"],
            failed_files=job_data["failed_files"],
            files=files,
            created_at=datetime.fromisoformat(job_data["created_at"]),
            updated_at=datetime.fromisoformat(job_data["updated_at"]),
            estimated_completion=datetime.fromisoformat(job_data["estimated_completion"]) if job_data.get("estimated_completion") else None
        )

    def process_docs_by_session(self, session_id: str) -> Optional[ProcessDocsResponse]:
        """Process documents by session ID and return file names and paths"""
        sessions = self._load_sessions()
        session_data = sessions.get(session_id)
        
        if not session_data:
            return None
        
        # Check if the associated job is completed
        job_id = session_data.get("job_id")
        jobs = self._load_jobs()
        job_data = jobs.get(job_id, {})
        job_status = job_data.get("status", "unknown")
        
        return ProcessDocsResponse(
            session_id=session_id,
            status=job_status,
            file_names=session_data["filenames"],
            total_files=len(session_data["filenames"]),
            message=f"Found {len(session_data['filenames'])} files for session {session_id}",
            lattice_headers=[],
            header_extraction_time=None,
            header_source="provided",
            lattice_results={},
            retrieval_time=None,
            total_processing_time=None
        )
    
    async def extract_headers_parallel(self, file_paths: List[str], lattice_header_count: int = 5) -> Dict[str, Any]:
        """
        Extract lattice headers from multiple documents in parallel.
        
        Args:
            file_paths: List of file paths to process
            lattice_header_count: Number of headers to extract
            
        Returns:
            Dict with extracted headers and metadata
        """
        start_time = time.time()
        
        print(f"🧠 Starting parallel lattice header extraction...")
        print(f"📁 Processing {len(file_paths)} documents")
        print(f"🎯 Target: {lattice_header_count} headers")
        print("-" * 50)
        
        try:
            # Call the lattice header extraction function
            result = await extract_lattice_headers(file_paths, lattice_header_count)
            
            extraction_time = time.time() - start_time
            
            # Extract the final headers from the nested result structure
            final_headers_result = result.get("final_headers_result", {})
            headers = final_headers_result.get("final_headers", [])
            
            print(f"🔍 Debug - Full result structure keys: {list(result.keys())}")
            print(f"🔍 Debug - Final headers result keys: {list(final_headers_result.keys())}")
            print(f"🔍 Debug - Final headers result success: {final_headers_result.get('success', False)}")
            if not final_headers_result.get('success', False):
                print(f"🔍 Debug - Final headers result error: {final_headers_result.get('error', 'No error message')}")
            
            print(f"✅ Header extraction completed in {extraction_time:.2f}s")
            print(f"🏷️ Extracted {len(headers)} headers: {headers}")
            print("-" * 50)
            
            return {
                "headers": headers,
                "extraction_time": extraction_time,
                "success": True,
                "individual_results": result.get("individual_results", []),
                "metadata": result
            }
            
        except Exception as e:
            extraction_time = time.time() - start_time
            print(f"❌ Header extraction failed after {extraction_time:.2f}s: {str(e)}")
            
            return {
                "headers": [],
                "extraction_time": extraction_time,
                "success": False,
                "error": str(e)
            }
    
    def perform_lattice_retrieval(self, headers: List[str], file_paths: List[str]) -> Dict[str, Any]:
        """
        Perform lattice retrieval using the provided headers and file paths.
        
        Args:
            headers: List of lattice headers to search for
            file_paths: List of file paths to search in
            
        Returns:
            Dict with retrieval results and metadata
        """
        start_time = time.time()
        
        print(f"🔍 Starting lattice retrieval...")
        print(f"🏷️ Headers ({len(headers)}): {headers}")
        print(f"📂 File paths ({len(file_paths)}):")
        for i, file_path in enumerate(file_paths, 1):
            print(f"   {i}. {file_path}")
        print("-" * 50)
        
        try:
            # Perform REAL parallel lattice retrieval!
            print(f"🚀 Calling parallel_lattice_retrieval with:")
            print(f"   🏷️ Headers: {headers}")
            print(f"   📂 PDF paths: {file_paths}")
            print(f"   🗃️ Collection: document_chunks")
            print(f"   🔢 K (results per header): 5")
            print(f"   ⚡ Max workers: 4")
            
            results = parallel_lattice_retrieval(
                lattice_headers=headers,
                pdf_paths=file_paths,
                collection_name="document_chunks",
                k=5,  # Top 5 results per header
                max_workers=4  # Parallel processing
            )
            
            print(f"🎉 Real lattice retrieval completed!")
            
            retrieval_time = time.time() - start_time
            
            print(f"✅ Lattice retrieval completed in {retrieval_time:.2f}s")
            print(f"📊 Retrieved results for {len(results) if isinstance(results, dict) else 0} headers")
            
            # Log detailed summary of real results
            if isinstance(results, dict):
                print(f"📋 Real lattice retrieval results breakdown:")
                for header, doc_results in results.items():
                    if isinstance(doc_results, dict):
                        # Real structure: {header: {doc_name: {answer, confidence_score, etc.}}}
                        doc_count = len(doc_results)
                        print(f"   🏷️ {header}: {doc_count} documents")
                        for doc_name, result in doc_results.items():
                            confidence = result.get('confidence_score', 0.0)
                            chunks = result.get('chunks_retrieved', 0)
                            print(f"      📄 {doc_name}: confidence={confidence:.2f}, chunks={chunks}")
                    else:
                        print(f"   🏷️ {header}: unexpected structure")
            
            print("-" * 50)
            
            return {
                "results": results,
                "retrieval_time": retrieval_time,
                "success": True,
                "headers_processed": len(headers),
                "files_searched": len(file_paths)
            }
            
        except Exception as e:
            retrieval_time = time.time() - start_time
            print(f"❌ Lattice retrieval failed after {retrieval_time:.2f}s: {str(e)}")
            
            return {
                "results": {},
                "retrieval_time": retrieval_time,
                "success": False,
                "error": str(e)
            }

    async def process_documents_real(self, job_id: str):
        """Background task to process documents using real document ingestion pipeline"""
        jobs = self._load_jobs()
        job_data = jobs.get(job_id)
        
        if not job_data:
            print(f"❌ Job {job_id} not found")
            return
        
        print(f"🚀 Starting real document processing for job {job_id}")
        
        try:
            # Get session data with file paths
            sessions = self._load_sessions()
            session_id = job_data.get("session_id")
            
            if not session_id or session_id not in sessions:
                raise Exception(f"Session {session_id} not found")
            
            session_data = sessions[session_id]
            file_paths = session_data.get("file_paths", [])
            
            if not file_paths:
                raise Exception("No file paths found in session")
            
            print(f"📄 Processing {len(file_paths)} documents with ingestion pipeline")
            print(f"📂 File paths to process:")
            for i, file_path in enumerate(file_paths, 1):
                print(f"   {i}. {file_path}")
            print(f"👤 User ID: {session_data.get('user_id', 'hotfix_user')}")
            print(f"🗃️ Collection: document_chunks")
            print(f"📏 Chunk size: 1280, Overlap: 256")
            print("-" * 60)
            
            # Mark all files as processing
            for file_data in job_data["files"]:
                file_data["status"] = "uploading"
                file_data["progress"] = 0
            
            job_data["status"] = "uploading"
            job_data["updated_at"] = datetime.now(timezone.utc).isoformat()
            jobs[job_id] = job_data
            self._save_jobs(jobs)
            
            # Run the actual document ingestion pipeline (async version)
            print(f"🔄 Calling run_batch_ingestion_pipeline...")
            ingestion_start_time = time.time()
            
            results = await run_batch_ingestion_pipeline(
                file_paths=file_paths,
                user_id=session_data.get("user_id", "hotfix_user"),
                collection_name="document_chunks",
                chunk_size=1280,
                chunk_overlap=256,
                cache_dir="pdf_cache"
            )
            
            ingestion_duration = time.time() - ingestion_start_time
            print(f"📊 Ingestion pipeline completed in {ingestion_duration:.2f}s")
            print(f"🔍 Processing {len(results)} results from run_batch_ingestion_pipeline")
            print("-" * 60)
            
            print(f"📋 DETAILED BATCH PROCESSING RESULTS:")
            print(f"🔍 Raw results returned: {results}")
            print(f"📊 Result type: {type(results)}")
            print(f"📏 Result length: {len(results) if hasattr(results, '__len__') else 'N/A'}")
            
            if results:
                print(f"🔍 First result structure: {results[0] if len(results) > 0 else 'No results'}")
                print(f"🔍 Result format: Expected (file_path, qdrant_success, graph_success)")
            
            print("-" * 60)
            
            # Update file statuses based on ingestion results
            completed_files = 0
            failed_files = 0
            
            print(f"🔄 Processing individual results:")
            
            # Match results by file path since the order should be the same
            for i, result_tuple in enumerate(results):
                print(f"\n📋 Result {i+1}:")
                print(f"   🔍 Raw tuple: {result_tuple}")
                print(f"   📊 Tuple type: {type(result_tuple)}")
                print(f"   📏 Tuple length: {len(result_tuple) if hasattr(result_tuple, '__len__') else 'N/A'}")
                
                try:
                    result_file_path, qdrant_success, graph_success = result_tuple
                    print(f"   📂 File path: {result_file_path}")
                    print(f"   🗃️ Qdrant success: {qdrant_success} (type: {type(qdrant_success)})")
                    print(f"   📈 Graph success: {graph_success} (type: {type(graph_success)})")
                    
                    if i < len(job_data["files"]):
                        file_data = job_data["files"][i]
                        
                        if qdrant_success:
                            file_data["status"] = "completed"
                            file_data["progress"] = 100
                            file_data["uploaded_at"] = datetime.now(timezone.utc).isoformat()
                            file_data["qdrant_path"] = result_file_path
                            completed_files += 1
                            print(f"   ✅ SUCCESS: {file_data['filename']} -> {result_file_path}")
                        else:
                            file_data["status"] = "failed"
                            file_data["error_message"] = "Qdrant ingestion failed"
                            failed_files += 1
                            print(f"   ❌ FAILED: {file_data['filename']} -> {result_file_path}")
                    else:
                        print(f"   ⚠️ No corresponding file data for result {i+1}")
                        
                except (ValueError, TypeError) as e:
                    print(f"   ❌ Error unpacking result tuple: {e}")
                    if i < len(job_data["files"]):
                        file_data = job_data["files"][i]
                        file_data["status"] = "failed"
                        file_data["error_message"] = f"Result parsing error: {e}"
                        failed_files += 1
            
            print("-" * 60)
            print(f"📊 FINAL PROCESSING SUMMARY:")
            print(f"⏱️ Total ingestion time: {ingestion_duration:.2f}s")
            print(f"📁 Total files processed: {len(results)}")
            print(f"✅ Successful files: {completed_files}")
            print(f"❌ Failed files: {failed_files}")
            print(f"📈 Success rate: {(completed_files/len(results)*100):.1f}%" if results else "0%")
            
            # Update job status
            job_data["completed_files"] = completed_files
            job_data["failed_files"] = failed_files
            
            if failed_files == 0:
                job_data["status"] = "completed"
                print(f"🎉 JOB COMPLETED: {job_id} - All {completed_files} files successfully processed!")
            else:
                job_data["status"] = "partially_completed"
                print(f"⚠️ JOB PARTIALLY COMPLETED: {job_id} - {completed_files} success, {failed_files} failed")
            
            job_data["updated_at"] = datetime.now(timezone.utc).isoformat()
            jobs[job_id] = job_data
            self._save_jobs(jobs)
            
            print(f"💾 Job status saved to database")
            print("-" * 60)
                
        except Exception as e:
            print(f"❌ Error in document processing: {e}")
            # Mark job as failed
            job_data["status"] = "failed"
            job_data["error_message"] = str(e)
            job_data["updated_at"] = datetime.now(timezone.utc).isoformat()
            
            # Mark all files as failed
            for file_data in job_data["files"]:
                if file_data["status"] in ["pending", "uploading"]:
                    file_data["status"] = "failed"
                    file_data["error_message"] = str(e)
            
            jobs[job_id] = job_data
            self._save_jobs(jobs)


# FastAPI App
app = FastAPI(
    title="Hotfix Upload API",
    description="Demo API for document upload simulation with job polling",
    version="1.0.0"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize service
hotfix_uploader = HotfixUploaderService()


@app.get("/")
async def root():
    return {
        "message": "Hotfix Upload API Server",
        "version": "1.0.0",
        "endpoints": {
            "available_files": "/available-files",
            "start_upload": "/upload",
            "job_status": "/job/{job_id}",
            "process_docs": "/process_docs"
        }
    }


@app.get("/available-files", response_model=AvailableFilesResponse)
async def get_available_files():
    """Get list of available files in mp_materials/pdfs/"""
    files = hotfix_uploader.get_available_files()
    return AvailableFilesResponse(
        files=files,
        total_count=len(files),
        source_directory=str(hotfix_uploader.source_dir.absolute())
    )


@app.post("/upload", response_model=HotfixStartResponse)
async def start_upload(
    request: HotfixUploadRequest,
    background_tasks: BackgroundTasks
):
    """Start uploading documents by name. Returns job ID and session ID for polling."""
    try:
        job_id, session_id, response = hotfix_uploader.start_upload_job(request.filenames)
        
        # Start background task for real document processing
        background_tasks.add_task(hotfix_uploader.process_documents_real, job_id)
        
        return response
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start upload: {str(e)}")


@app.get("/job/{job_id}", response_model=HotfixJobResponse)
async def get_job_status(job_id: str):
    """Get current status of upload job."""
    job_status = hotfix_uploader.get_job_status(job_id)
    
    if not job_status:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return job_status


@app.post("/process_docs", response_model=ProcessDocsResponse)
async def process_docs(request: ProcessDocsRequest):
    """Complete lattice analysis: extract headers + perform retrieval."""
    total_start_time = time.time()
    
    print(f"\n🚀 Starting complete lattice analysis for session: {request.session_id}")
    print(f"🏷️ Headers provided: {bool(request.lattice_headers)}")
    if request.lattice_headers:
        print(f"📋 Provided headers ({len(request.lattice_headers)}): {request.lattice_headers}")
    print("=" * 80)
    
    try:
        # Get session data
        sessions = hotfix_uploader._load_sessions()
        session_data = sessions.get(request.session_id)
        
        if not session_data:
            print(f"❌ Session {request.session_id} not found")
            raise HTTPException(status_code=404, detail="Session not found")
        
        file_paths = session_data.get("file_paths", [])
        if not file_paths:
            print(f"❌ No file paths found in session")
            raise HTTPException(status_code=400, detail="No file paths found in session")
        
        print(f"✅ Session found with {len(file_paths)} files")
        
        # Get job status
        job_id = session_data.get("job_id")
        jobs = hotfix_uploader._load_jobs()
        job_data = jobs.get(job_id, {})
        job_status = job_data.get("status", "unknown")
        print(f"📊 Job status: {job_status}")
        
        # PHASE 1: Get Headers (provided or extracted)
        print(f"\n🔶 PHASE 1: HEADER ACQUISITION")
        print("-" * 40)
        
        header_extraction_time = 0.0
        header_source = "provided"
        
        if request.lattice_headers:
            print(f"🎯 Path A: Using provided headers ({len(request.lattice_headers)} headers)")
            headers = request.lattice_headers
            header_extraction_time = 0.0
            header_source = "provided"
        else:
            print(f"🎯 Path B: Extracting headers from documents")
            
            # Extract headers in parallel
            extraction_result = await hotfix_uploader.extract_headers_parallel(
                file_paths=file_paths,
                lattice_header_count=10  # Extract 10 headers
            )
            
            if extraction_result["success"]:
                headers = extraction_result["headers"]
                header_extraction_time = extraction_result["extraction_time"]
                header_source = "extracted"
                
                print(f"✅ Header extraction successful!")
                print(f"🏷️ Extracted headers: {headers}")
            else:
                headers = []
                header_extraction_time = extraction_result["extraction_time"]
                header_source = "extraction_failed"
                
                print(f"❌ Header extraction failed: {extraction_result.get('error', 'Unknown error')}")
        
        # PHASE 2: Lattice Retrieval
        print(f"\n🔶 PHASE 2: LATTICE RETRIEVAL")
        print("-" * 40)
        
        lattice_results = {}
        retrieval_time = 0.0
        
        if headers:
            # Perform lattice retrieval
            retrieval_result = hotfix_uploader.perform_lattice_retrieval(
                headers=headers,
                file_paths=file_paths
            )
            
            if retrieval_result["success"]:
                lattice_results = retrieval_result["results"]
                retrieval_time = retrieval_result["retrieval_time"]
                
                print(f"✅ Lattice retrieval successful!")
                print(f"📊 Retrieved results for {retrieval_result['headers_processed']} headers")
            else:
                print(f"❌ Lattice retrieval failed: {retrieval_result.get('error', 'Unknown error')}")
                lattice_results = {}
                retrieval_time = retrieval_result["retrieval_time"]
        else:
            print(f"⚠️ Skipping lattice retrieval - no headers available")
        
        # PHASE 3: Final Response
        print(f"\n🔶 PHASE 3: RESPONSE GENERATION")
        print("-" * 40)
        
        total_processing_time = time.time() - total_start_time
        
        # Determine message based on results
        if headers and lattice_results:
            message = f"Complete analysis: Found {len(session_data['filenames'])} files, {len(headers)} headers, and retrieved lattice data"
        elif headers:
            message = f"Partial analysis: Found {len(session_data['filenames'])} files and {len(headers)} headers, but retrieval failed"
        else:
            message = f"Basic analysis: Found {len(session_data['filenames'])} files, but header extraction failed"
        
        response = ProcessDocsResponse(
            session_id=request.session_id,
            status=job_status,
            file_names=session_data["filenames"],
            total_files=len(session_data["filenames"]),
            message=message,
            lattice_headers=headers,
            header_extraction_time=header_extraction_time,
            header_source=header_source,
            lattice_results=lattice_results,
            retrieval_time=retrieval_time,
            total_processing_time=total_processing_time
        )
        
        print(f"✅ Complete lattice analysis finished!")
        print(f"⏱️ Total time: {total_processing_time:.2f}s")
        print(f"   📊 Header extraction: {header_extraction_time:.2f}s")
        print(f"   🔍 Lattice retrieval: {retrieval_time:.2f}s")
        print(f"🏷️ Headers: {len(headers)}")
        print(f"📋 Results: {len(lattice_results)} header groups")
        print("=" * 80)
        
        return response
    
    except HTTPException:
        raise
    except Exception as e:
        total_processing_time = time.time() - total_start_time
        print(f"❌ Unexpected error in process_docs after {total_processing_time:.2f}s: {str(e)}")
        print("=" * 80)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc)}


if __name__ == "__main__":
    print("🚀 Starting Hotfix Upload API Server...")
    print("📁 Available files will be loaded from: mp_materials/Investor Relations/pdfs/")
    print("💾 Job data will be stored in: hotfix_data/")
    print("🌐 Server will be available at: http://localhost:8001")
    print("📚 API docs will be available at: http://localhost:8001/docs")
    print()
    
    uvicorn.run(
        "hotfix_api:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )