from fastapi import APIRouter, Depends, HTTPException, BackgroundT<PERSON><PERSON>, Head<PERSON>
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid
from datetime import datetime
import json

from api.models.database import get_db, UploadSession, UploadedFile, FileStatus, UploadStatus, to_db_id, from_db_id
from api.schemas.upload import (
    PrepareUploadRequest, PrepareUploadResponse, PresignedUrlMapping,
    CompleteUploadRequest, SessionStatusResponse, ProcessResponse,
    FileInfo, FileMapping,
    HotfixUploadRequest, HotfixJobResponse, HotfixStartResponse, AvailableFilesResponse
)
from api.services.storage import storage_service
from api.services.processor import file_processor
from api.services.hotfix_uploader import hotfix_uploader
from api.utils.logger import logger

router = APIRouter(prefix="/upload", tags=["upload"])


@router.post("/prepare", response_model=PrepareUploadResponse)
async def prepare_upload(
    request: PrepareUploadRequest,
    user_id: Optional[str] = None,
    company_name: Optional[str] = None,
    session_type: Optional[str] = None,
    db: Session = Depends(get_db)
) -> PrepareUploadResponse:
    """
    Prepare for file uploads by creating a session and generating presigned URLs
    """
    try:
        # Create new session with metadata
        session = UploadSession(
            total_files=len(request.filenames),
            user_id=user_id,
            company_name=company_name,
            session_type=session_type,
            session_metadata={
                "source": "api",
                "filenames": request.filenames
            } if "postgresql" in db.bind.url.drivername else json.dumps({
                "source": "api",
                "filenames": request.filenames
            })
        )
        db.add(session)
        db.commit()
        
        logger.info(f"Created upload session {session.id} for {len(request.filenames)} files")
        
        # Generate presigned URLs for each file
        presigned_urls = []
        
        for filename in request.filenames:
            # Create file record
            file = UploadedFile(
                session_id=session.id,
                filename=filename,
                bucket_key=storage_service.generate_unique_key(from_db_id(session.id), filename)
            )
            db.add(file)
            db.flush()  # Flush to get the auto-generated ID
            
            # Generate presigned URL
            upload_url = storage_service.generate_presigned_url(
                object_key=file.bucket_key,
                content_type="application/octet-stream"  # Default, client can override
            )
            
            if not upload_url:
                logger.error(f"Failed to generate presigned URL for {filename}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to generate upload URL for {filename}"
                )
            
            presigned_urls.append(PresignedUrlMapping(
                filename=filename,
                upload_url=upload_url,
                file_id=from_db_id(file.id),
                bucket_key=file.bucket_key
            ))
        
        db.commit()
        
        return PrepareUploadResponse(
            session_id=from_db_id(session.id),
            presigned_urls=presigned_urls,
            expires_in_seconds=storage_service.client._client_config.signature_version == 's3v4' and 900 or 3600
        )
        
    except Exception as e:
        logger.error(f"Error preparing upload: {str(e)}", exc_info=True)
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/complete", response_model=SessionStatusResponse)
async def complete_upload(
    request: CompleteUploadRequest,
    db: Session = Depends(get_db)
) -> SessionStatusResponse:
    """
    Mark files as uploaded and update their metadata
    """
    try:
        # Get session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(request.session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Update file records
        file_map = {f.filename: f for f in session.files}
        
        for file_info in request.files:
            if file_info.filename not in file_map:
                logger.warning(f"File {file_info.filename} not found in session")
                continue
            
            file = file_map[file_info.filename]
            
            # Verify file exists in storage
            if storage_service.check_object_exists(file.bucket_key):
                file.status = FileStatus.UPLOADED
                file.bucket_id = file_info.bucket_id
                file.file_size = file_info.file_size
                file.content_type = file_info.content_type
                
                # Get actual metadata from storage
                metadata = storage_service.get_object_metadata(file.bucket_key)
                if metadata:
                    file.file_size = metadata['size']
                    file.content_type = metadata['content_type']
            else:
                file.status = FileStatus.FAILED
                file.error_message = "File not found in storage"
        
        db.commit()
        
        # Return updated session status
        return _build_session_response(session)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing upload: {str(e)}", exc_info=True)
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/process/{session_id}", response_model=ProcessResponse)
async def process_files(
    session_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> ProcessResponse:
    """
    Start processing files in a session
    """
    try:
        # Get session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Check if already processing
        if session.status in [UploadStatus.PROCESSING, UploadStatus.COMPLETED]:
            return ProcessResponse(
                session_id=session_id,
                status=session.status.value,
                message=f"Session is already {session.status.value}"
            )
        
        # Count uploaded files
        uploaded_files = sum(1 for f in session.files if f.status == FileStatus.UPLOADED)
        
        if uploaded_files == 0:
            raise HTTPException(
                status_code=400,
                detail="No files have been uploaded yet"
            )
        
        # Add background task to process files
        background_tasks.add_task(
            file_processor.process_session,
            session_id
        )
        
        return ProcessResponse(
            session_id=session_id,
            status="processing",
            message=f"Processing started for {uploaded_files} files"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting processing: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{session_id}", response_model=SessionStatusResponse)
async def get_session_status(
    session_id: str,
    db: Session = Depends(get_db)
) -> SessionStatusResponse:
    """
    Get the current status of an upload session
    """
    try:
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return _build_session_response(session)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session status: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))




@router.get("/results/{session_id}")
async def get_processing_results(
    session_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed processing results for a session
    """
    try:
        # Get detailed session info with processing results
        session_info = file_processor.get_session_info(session_id, db)
        
        if not session_info:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return session_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting processing results: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


def _build_session_response(session: UploadSession) -> SessionStatusResponse:
    """Helper to build session response"""
    files = []
    for file in session.files:
        files.append(FileInfo(
            id=from_db_id(file.id),
            filename=file.filename,
            status=file.status,
            file_size=file.file_size,
            content_type=file.content_type,
            created_at=file.created_at,
            error_message=file.error_message
        ))
    
    return SessionStatusResponse(
        session_id=from_db_id(session.id),
        status=session.status,
        total_files=session.total_files,
        processed_files=session.processed_files,
        created_at=session.created_at,
        updated_at=session.updated_at,
        files=files,
        error_message=session.error_message
    )


# HOTFIX UPLOAD ENDPOINTS

@router.get("/hotfix/available-files", response_model=AvailableFilesResponse)
async def get_available_files():
    """
    Get list of available files in mp_materials/Investor Relations/pdfs directory
    """
    try:
        files = hotfix_uploader.get_available_files()
        
        return AvailableFilesResponse(
            files=files,
            total_count=len(files),
            source_directory="mp_materials/Investor Relations/pdfs"
        )
        
    except Exception as e:
        logger.error(f"Error getting available files: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/hotfix", response_model=HotfixStartResponse)
async def start_hotfix_upload(
    request: HotfixUploadRequest,
    background_tasks: BackgroundTasks
) -> HotfixStartResponse:
    """
    Start a hotfix upload job for demo purposes.
    Takes document names and simulates uploading them from the local mp_materials directory.
    """
    try:
        logger.info(f"Starting hotfix upload for {len(request.filenames)} files")
        
        # Create upload job
        job_data = hotfix_uploader.create_upload_job(request.filenames)
        job_id = job_data["job_id"]
        
        # Start background upload simulation
        background_tasks.add_task(hotfix_uploader.start_background_upload, job_id)
        
        return HotfixStartResponse(
            job_id=job_id,
            status="uploading",
            message=f"Upload started for {len(request.filenames)} files",
            total_files=len(request.filenames)
        )
        
    except ValueError as e:
        logger.error(f"Validation error in hotfix upload: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error starting hotfix upload: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/hotfix/status/{job_id}", response_model=HotfixJobResponse)
async def get_hotfix_job_status(job_id: str) -> HotfixJobResponse:
    """
    Get the current status of a hotfix upload job
    """
    try:
        job_data = hotfix_uploader.get_job_status(job_id)
        
        if not job_data:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return HotfixJobResponse(**job_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting hotfix job status: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))