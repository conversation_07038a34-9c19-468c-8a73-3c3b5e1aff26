from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict
from datetime import datetime
from enum import Enum


class UploadStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class FileStatus(str, Enum):
    PENDING = "pending"
    UPLOADED = "uploaded"
    PROCESSED = "processed"
    FAILED = "failed"


# Request schemas
class PrepareUploadRequest(BaseModel):
    filenames: List[str] = Field(..., min_items=1, max_items=1000)
    
    class Config:
        json_schema_extra = {
            "example": {
                "filenames": ["document1.pdf", "document2.pdf", "report.xlsx"]
            }
        }


class FileMapping(BaseModel):
    filename: str
    bucket_id: str
    file_size: Optional[int] = None
    content_type: Optional[str] = None


class CompleteUploadRequest(BaseModel):
    session_id: str
    files: List[FileMapping]
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "files": [
                    {
                        "filename": "document1.pdf",
                        "bucket_id": "uploads/123e4567/document1.pdf",
                        "file_size": 1024000,
                        "content_type": "application/pdf"
                    }
                ]
            }
        }


# Response schemas
class PresignedUrlMapping(BaseModel):
    filename: str
    upload_url: str
    file_id: str
    bucket_key: str


class PrepareUploadResponse(BaseModel):
    session_id: str
    presigned_urls: List[PresignedUrlMapping]
    expires_in_seconds: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "presigned_urls": [
                    {
                        "filename": "document1.pdf",
                        "upload_url": "https://spaces.digitalocean.com/...",
                        "file_id": "file_123",
                        "bucket_key": "uploads/123e4567/document1.pdf"
                    }
                ],
                "expires_in_seconds": 900
            }
        }


class FileInfo(BaseModel):
    id: str
    filename: str
    status: FileStatus
    file_size: Optional[int] = None
    content_type: Optional[str] = None
    created_at: datetime
    error_message: Optional[str] = None


class SessionStatusResponse(BaseModel):
    session_id: str
    status: UploadStatus
    total_files: int
    processed_files: int
    created_at: datetime
    updated_at: datetime
    files: List[FileInfo]
    error_message: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "processing",
                "total_files": 3,
                "processed_files": 1,
                "created_at": "2024-01-14T10:00:00Z",
                "updated_at": "2024-01-14T10:05:00Z",
                "files": [
                    {
                        "id": "file_123",
                        "filename": "document1.pdf",
                        "status": "processed",
                        "file_size": 1024000,
                        "content_type": "application/pdf",
                        "created_at": "2024-01-14T10:00:00Z"
                    }
                ]
            }
        }


class ProcessResponse(BaseModel):
    session_id: str
    status: str
    message: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "processing",
                "message": "Processing started for 3 files"
            }
        }


class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "detail": "Session not found",
                "error_code": "SESSION_NOT_FOUND"
            }
        }


# Hotfix Upload Schemas
class HotfixUploadRequest(BaseModel):
    filenames: List[str] = Field(..., min_items=1, max_items=50)
    
    class Config:
        json_schema_extra = {
            "example": {
                "filenames": ["Annual Report.pdf", "Quarterly Report.pdf", "Current report filing.pdf"]
            }
        }


class HotfixFileInfo(BaseModel):
    filename: str
    status: str  # pending, uploading, completed, failed
    progress: int = Field(ge=0, le=100)
    file_size: Optional[int] = None
    bucket_key: Optional[str] = None
    bucket_url: Optional[str] = None
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class HotfixJobResponse(BaseModel):
    job_id: str
    status: str  # uploading, completed, failed
    total_files: int
    completed_files: int
    failed_files: int
    files: List[HotfixFileInfo]
    created_at: datetime
    updated_at: datetime
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "job_id": "hotfix_a1b2c3d4",
                "status": "uploading",
                "total_files": 3,
                "completed_files": 1,
                "failed_files": 0,
                "files": [
                    {
                        "filename": "Annual Report.pdf",
                        "status": "completed",
                        "progress": 100,
                        "file_size": 16777216,
                        "bucket_url": "https://quantera.sgp1.digitaloceanspaces.com/uploads/hotfix/hotfix_a1b2c3d4/Annual_Report.pdf"
                    },
                    {
                        "filename": "Quarterly Report.pdf",
                        "status": "uploading",
                        "progress": 75,
                        "file_size": 651264
                    }
                ],
                "created_at": "2024-01-14T10:00:00Z",
                "updated_at": "2024-01-14T10:05:00Z"
            }
        }


class HotfixStartResponse(BaseModel):
    job_id: str
    status: str
    message: str
    total_files: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "job_id": "hotfix_a1b2c3d4",
                "status": "uploading",
                "message": "Upload started for 3 files",
                "total_files": 3
            }
        }


class AvailableFileInfo(BaseModel):
    filename: str
    file_size: int
    modified_at: datetime
    exists: bool


class AvailableFilesResponse(BaseModel):
    files: List[AvailableFileInfo]
    total_count: int
    source_directory: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "files": [
                    {
                        "filename": "Annual Report.pdf",
                        "file_size": 16777216,
                        "modified_at": "2024-01-14T10:00:00Z",
                        "exists": True
                    }
                ],
                "total_count": 45,
                "source_directory": "mp_materials/Investor Relations/pdfs"
            }
        }