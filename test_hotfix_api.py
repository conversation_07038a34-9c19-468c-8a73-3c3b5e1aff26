#!/usr/bin/env python3
"""
Test script for the hotfix upload API endpoints
"""

import requests
import time
import json
from typing import Dict, Any

# API Configuration
API_BASE = "http://localhost:8001"
HOTFIX_BASE = f"{API_BASE}"

def test_available_files():
    """Test getting available files"""
    print("🔍 Testing available files endpoint...")
    
    response = requests.get(f"{HOTFIX_BASE}/available-files")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Found {data['total_count']} available files")
        print(f"📁 Source: {data['source_directory']}")
        
        # Show first few files
        for i, file_info in enumerate(data['files'][:5]):
            size_mb = file_info['file_size'] / (1024 * 1024)
            print(f"   {i+1}. {file_info['filename']} ({size_mb:.1f} MB)")
        
        if len(data['files']) > 5:
            print(f"   ... and {len(data['files']) - 5} more files")
        
        return data['files']
    else:
        print(f"❌ Failed to get available files: {response.status_code}")
        print(f"   Error: {response.text}")
        return []

def test_upload_start(filenames):
    """Test starting an upload job"""
    print(f"\n🚀 Testing upload start with {len(filenames)} files...")
    
    payload = {"filenames": filenames}
    response = requests.post(f"{HOTFIX_BASE}/upload", json=payload)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Upload job started: {data['job_id']}")
        print(f"🎫 Session ID: {data['session_id']}")
        print(f"📊 Status: {data['status']}")
        print(f"📝 Message: {data['message']}")
        return data['job_id'], data['session_id']
    else:
        print(f"❌ Failed to start upload: {response.status_code}")
        print(f"   Error: {response.text}")
        return None, None

def test_job_polling(job_id: str):
    """Test polling job status until completion (no time limit)"""
    print(f"\n📊 Polling job status: {job_id}")
    
    start_time = time.time()
    poll_count = 0
    
    while True:
        poll_count += 1
        elapsed_time = time.time() - start_time
        
        response = requests.get(f"{HOTFIX_BASE}/job/{job_id}")
        
        if response.status_code == 200:
            data = response.json()
            status = data['status']
            completed = data['completed_files']
            total = data['total_files']
            
            print(f"   Poll {poll_count} ({elapsed_time:.1f}s): {status} - {completed}/{total} files completed")
            
            # Show file progress
            for file_info in data['files']:
                filename = file_info['filename']
                file_status = file_info['status']
                progress = file_info['progress']
                
                if file_status == 'uploading':
                    print(f"     📤 {filename}: {progress}%")
                elif file_status == 'completed':
                    print(f"     ✅ {filename}: completed")
                elif file_status == 'failed':
                    print(f"     ❌ {filename}: failed - {file_info.get('error_message', 'Unknown error')}")
                else:
                    print(f"     ⏳ {filename}: pending")
            
            if status in ['completed', 'failed', 'partially_completed']:
                total_time = time.time() - start_time
                print(f"\n🎯 Job {status} after {total_time:.1f} seconds!")
                if status in ['completed', 'partially_completed']:
                    print("📋 Final results:")
                    completed_count = 0
                    failed_count = 0
                    for file_info in data['files']:
                        if file_info['status'] == 'completed':
                            uploaded_at = file_info.get('uploaded_at', 'Unknown time')
                            print(f"   ✅ {file_info['filename']}: successfully ingested at {uploaded_at}")
                            completed_count += 1
                        elif file_info['status'] == 'failed':
                            error_msg = file_info.get('error_message', 'Unknown error')
                            print(f"   ❌ {file_info['filename']}: failed - {error_msg}")
                            failed_count += 1
                    
                    print(f"📊 Summary: {completed_count} successful, {failed_count} failed")
                    print(f"⏱️ Total processing time: {total_time:.1f} seconds")
                return data
            
            print("   ⏰ Waiting 3 seconds before next poll...\n")
            time.sleep(3)
        else:
            print(f"   ❌ Failed to get job status: {response.status_code}")
            print(f"      Error: {response.text}")
            return None


def test_process_docs(session_id: str):
    """Test processing documents by session ID"""
    print(f"\n📄 Testing process_docs with session: {session_id}")
    
    payload = {"session_id": session_id}
    response = requests.post(f"{HOTFIX_BASE}/process_docs", json=payload)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Documents processed successfully")
        print(f"🎫 Session ID: {data['session_id']}")
        print(f"📊 Status: {data['status']}")
        print(f"📁 Total files: {data['total_files']}")
        print(f"📝 Message: {data['message']}")
        print(f"📋 File names:")
        for i, filename in enumerate(data['file_names'], 1):
            print(f"   {i}. {filename}")
        print(f"🗂️ File paths:")
        for i, file_path in enumerate(data['file_paths'], 1):
            print(f"   {i}. {file_path}")
        return data
    else:
        print(f"❌ Failed to process docs: {response.status_code}")
        print(f"   Error: {response.text}")
        return None


def main():
    """Main test function"""
    print("🧪 HOTFIX UPLOAD API TEST\n")
    
    # Test 1: Get available files
    available_files = test_available_files()
    
    if not available_files:
        print("❌ Cannot proceed without available files")
        return
    
    # Test 2: Start upload with a few files
    test_files = [
        "Annual Report.pdf",
        "Quarterly Report.pdf",
        "Current report filing.pdf"
    ]
    
    # Filter to only use files that actually exist
    existing_files = [f["filename"] for f in available_files]
    valid_test_files = [f for f in test_files if f in existing_files]
    
    if not valid_test_files:
        print("❌ None of the test files exist, using first 2 available files")
        valid_test_files = [f["filename"] for f in available_files[:2]]
    
    print(f"\n🎯 Using test files: {valid_test_files}")
    
    job_id, session_id = test_upload_start(valid_test_files)
    
    if not job_id or not session_id:
        print("❌ Cannot proceed without valid job ID and session ID")
        return
    
    # Test 3: Test process_docs immediately (even before upload completes)
    process_result = test_process_docs(session_id)
    if not process_result:
        print("❌ Process docs test failed")
        return
    
    # Test 4: Poll job status
    final_result = test_job_polling(job_id)
    
    if final_result:
        print("✅ Upload test completed successfully!")
        
        # Test 5: Test process_docs again after completion
        print(f"\n🔄 Testing process_docs again after job completion...")
        final_process_result = test_process_docs(session_id)
        if final_process_result:
            print("🎉 All tests passed successfully!")
        else:
            print("❌ Final process_docs test failed")
    else:
        print("❌ Test failed during polling")

if __name__ == "__main__":
    main()