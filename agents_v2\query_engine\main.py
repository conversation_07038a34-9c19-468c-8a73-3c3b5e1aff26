import os
import json
import faiss  # Kept for potential future use - currently using Qdrant instead
import requests
import numpy as np
import time
import hashlib
import asyncio
import aiofiles
import base64
import uuid  # Added for UUID generation
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from nltk.tokenize import word_tokenize
from rank_bm25 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from openai import OpenAI
from dotenv import load_dotenv
from typing import List, Dict, Tuple, Any
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue

# Download required NLTK data
import nltk
try:
    # Try to find punkt first
    nltk.data.find('tokenizers/punkt')
except LookupError:
    print("Downloading NLTK punkt tokenizer...")
    nltk.download('punkt', quiet=True)
    print("✅ NLTK punkt tokenizer downloaded successfully")

# Simple tokenization function that doesn't rely on NLTK data
def simple_tokenize(text):
    """Simple word tokenization that splits on whitespace and punctuation."""
    import re
    # Split on whitespace and common punctuation
    tokens = re.findall(r'\b\w+\b', text.lower())
    return tokens

# Use simple tokenization instead of NLTK
word_tokenize = simple_tokenize

#  Load environment variables
load_dotenv()

# Configuration 
JINA_API_URL = os.getenv("JINA_API_URL", "https://api.jina.ai/v1/embeddings")
JINA_API_KEY = os.getenv("JINA_API_KEY")
MODEL_NAME = os.getenv("MODEL_NAME")

# Qdrant Configuration
QDRANT_URL = os.getenv("QDRANT_URL", "https://3312ccd1-6af5-4b15-8e3e-60ddbdd7b38a.europe-west3-0.gcp.cloud.qdrant.io:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
QDRANT_COLLECTION_NAME = os.getenv("QDRANT_COLLECTION_NAME", "document_chunks")

# Initialize the OpenAI client
client = OpenAI(
    base_url=os.getenv("AGENTIC_BASE_URL"),
    api_key=os.getenv("AGENTIC_API_KEY")
)

# Initialize Qdrant client with timeout settings
qdrant_client = QdrantClient(
    url=QDRANT_URL,
    api_key=QDRANT_API_KEY,
    timeout=60,  # 60 seconds timeout for operations
)

# Cache directory for parsed PDFs
CACHE_DIR = Path(__file__).parent / "cached_data"
CACHE_DIR.mkdir(exist_ok=True)

# Semaphore to limit concurrent Qdrant operations
qdrant_semaphore = asyncio.Semaphore(2)  # Allow max 2 concurrent Qdrant operations


# PDF PARSING & CACHING 

def get_file_hash(file_path: str) -> str:
    """Generate a unique hash for a file to use as a cache key."""
    with open(file_path, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def get_cache_path(pdf_path: str) -> Path:
    """Get the cache file path for a given PDF."""
    file_hash = get_file_hash(pdf_path)
    filename = Path(pdf_path).stem
    return CACHE_DIR / f"{filename}_{file_hash}.txt"

async def get_cached_content(pdf_path: str) -> str:
    """Retrieve cached content for a PDF if it exists."""
    cache_path = get_cache_path(pdf_path)
    if cache_path.exists():
        print(f"✓ Using cached content for {Path(pdf_path).name}")
        async with aiofiles.open(cache_path, 'r', encoding='utf-8') as f:
            return await f.read()
    return None

async def cache_content(pdf_path: str, content: str):
    """Cache the parsed content to a text file."""
    cache_path = get_cache_path(pdf_path)
    async with aiofiles.open(cache_path, 'w', encoding='utf-8') as f:
        await f.write(content)
    print(f"✓ Cached content for {Path(pdf_path).name}")

def parse_pdf_with_jina(pdf_path: str, max_retries: int = 3) -> str:
    """
    Parse an entire PDF using the JINA AI Reader API with robust retry logic.
    This function is synchronous and designed to be run in a thread pool.
    """
    print(f"📄 Parsing {Path(pdf_path).name} with JINA...")

    try:
        with open(pdf_path, 'rb') as f:
            pdf_bytes = f.read()
    except Exception as e:
        raise ValueError(f"Failed to read PDF file: {str(e)}")

    file_size_mb = len(pdf_bytes) / (1024 * 1024)
    if file_size_mb > 50:  # JINA's recommended limit
        print(f"⚠️  Large file detected ({file_size_mb:.1f}MB) - parsing may take longer.")

    pdf_base64 = base64.b64encode(pdf_bytes).decode('utf-8')

    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'HybridRAGLattice/1.0'
    }
    if JINA_API_KEY:
        headers["Authorization"] = f"Bearer {JINA_API_KEY}"

    payload = {
        "url": None,
        "pdf": pdf_base64,
        "filename": Path(pdf_path).name
    }

    for attempt in range(max_retries):
        try:
            timeout = min(300, max(60, int(file_size_mb * 10)))
            print(f"🔄 Attempt {attempt + 1}/{max_retries} (timeout: {timeout}s)")

            response = requests.post("https://r.jina.ai/", json=payload, headers=headers, timeout=timeout)

            if response.status_code == 200:
                content = response.text.strip()
                if content and len(content) > 50 and "Access denied" not in content and "Cloudflare" not in content:
                    print(f"✓ Successfully parsed {Path(pdf_path).name} ({len(content)} chars)")
                    return content
                else:
                    raise ValueError("Invalid or empty response from JINA API.")

            elif response.status_code in [429, 503, 502, 504]:
                wait_time = (2 ** attempt) + np.random.uniform(0, 1)
                print(f"⚠️  JINA API Error ({response.status_code}). Retrying in {wait_time:.2f}s...")
                if attempt < max_retries - 1:
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API persistently failed with status {response.status_code}: {response.text}")
            else:
                raise ValueError(f"JINA API error: {response.status_code} - {response.text}")

        except requests.exceptions.RequestException as e:
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"⚠️  Connection error: {e}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            else:
                raise ValueError(f"Network error after {max_retries} attempts: {e}")

    raise ValueError(f"Failed to parse PDF with JINA after {max_retries} attempts.")

async def get_or_parse_pdf_content(pdf_path: str) -> str:
    """
    Orchestrator to get PDF content, preferring cache but parsing if needed.
    """
    cached_content = await get_cached_content(pdf_path)
    if cached_content:
        return cached_content

    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as executor:
        content = await loop.run_in_executor(executor, parse_pdf_with_jina, pdf_path)

    await cache_content(pdf_path, content)
    return content


# LATTICE HEADER EXTRACTION

async def extract_headers_from_document(pdf_path: str, content: str, lattice_count: int = 20) -> Dict[str, Any]:
    """Extract potential headers from a document's content using an LLM."""
    filename = Path(pdf_path).name
    print(f"🤖 Extracting headers from {filename}...")

    # Truncate content to avoid token limits
    max_content_length = 8000  
    if len(content) > max_content_length:
        content = content[:max_content_length] + "... [content truncated]"
        print(f"⚠️  Content truncated to {max_content_length} characters for {filename}")

    prompt = f"""
Analyze this document and extract {lattice_count} meaningful column headers that could be used to extract structured data.

Document filename: {filename}
Document content (first part):
{content}

Instructions:
1. **Document Type Analysis**: First, identify the type of document (financial report, contract, resume, research paper, legal document, etc.) and extract headers relevant to that specific document type.

2. **Content-Based Extraction**: Look for:
   - Actual data fields, metrics, or values mentioned in the document
   - Section headers, table columns, or structured information
   - Key entities, dates, numbers, or categorical information
   - Any information that could be systematically extracted and compared across documents

3. **Headers should be**:
   - Specific enough to be meaningful and actionable
   - General enough to be reusable across similar documents
   - Professional and clear
   - Suitable as Excel column headers
   - Based on actual content present in the document

4. **Consider different document types and their relevant headers**:
   - **Financial Documents**: Revenue, Net Income, EBITDA, Assets, Liabilities, Cash Flow, Fiscal Year, Quarter, etc.
   - **Legal Documents**: Contract Value, Effective Date, Parties, Terms, Jurisdiction, Case Number, Filing Date, etc.
   - **Resumes/CVs**: Name, Experience Years, Skills, Education, Previous Companies, Certifications, etc.
   - **Research Papers**: Title, Authors, Abstract, Methodology, Results, Conclusions, Publication Date, etc.
   - **Reports**: Report Type, Author, Key Findings, Recommendations, Executive Summary, etc.
   - **Technical Documents**: Specifications, Requirements, Standards, Compliance Status, etc.
   - **Marketing Materials**: Campaign Name, Target Audience, Budget, ROI, Conversion Rate, etc.
   - **Operational Documents**: Process Name, Department, Timeline, Resources, Outcomes, etc.

5. **Extraction Guidelines**:
   - Focus on structured, extractable data rather than narrative text
   - Look for patterns, lists, tables, or repeated information structures
   - Identify both explicit headers and implicit data categories
   - Consider temporal, numerical, categorical, and entity-based information
   - Extract headers that represent measurable or comparable data points

6. **Quality Control**:
   - Avoid overly generic headers like "Information" or "Data"
   - Ensure headers are specific to the document's content and purpose
   - Prefer headers that would have concrete values (dates, numbers, names, categories)
   - Limit to {lattice_count} most relevant headers per document

CRITICAL: You must respond with ONLY a valid JSON object in this exact format:
{{
    "headers": ["Header 1", "Header 2", "Header 3", ...]
}}

No other text, explanations, or formatting. Just the JSON.
"""

    try:
        response = await asyncio.to_thread(
            client.chat.completions.create,
            model="agentic-large",
            messages=[
                {"role": "system", "content": "You are a document analyst. Respond with valid JSON only."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=1000
        )
        
        response_text = response.choices[0].message.content.strip()
        print(f"🔍 LLM Response for {filename}: {response_text[:200]}...")  
        
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1
        
        if json_start != -1 and json_end != -1:
            json_text = response_text[json_start:json_end]
            result = json.loads(json_text)
        else:
            result = json.loads(response_text)

        if "headers" in result and isinstance(result["headers"], list) and len(result["headers"]) > 0:
            print(f"✓ Extracted {len(result['headers'])} headers from {filename}")
            return {"filename": filename, "pdf_path": pdf_path, "headers": result["headers"], "success": True, "error": None}
        else:
            print(f"⚠️  No valid headers found in response for {filename}")
            return {"filename": filename, "pdf_path": pdf_path, "headers": [], "success": False, "error": "No valid headers found"}

    except json.JSONDecodeError as e:
        print(f"✗ JSON decode error for {filename}: {e}")
        print(f"Raw response: {response_text}")
        return {"filename": filename, "pdf_path": pdf_path, "headers": [], "success": False, "error": f"JSON decode error: {e}"}
    
    except Exception as e:
        print(f"✗ Error extracting headers from {filename}: {e}")
        return {"filename": filename, "pdf_path": pdf_path, "headers": [], "success": False, "error": str(e)}

async def reduce_headers_to_final_set(individual_results: List[Dict[str, Any]], lattice_count: int = 10) -> Dict[str, Any]:
    """Consolidate headers from multiple documents into a final, unified set using an LLM."""
    print("🧠 Reducing all generated headers to a final consolidated set...")

    all_headers = []
    for result in individual_results:
        if result["success"] and result["headers"]:
            all_headers.extend(result["headers"])
    
    if not all_headers:
        return {"final_headers": [], "success": False, "error": "No headers were extracted to reduce."}

    # Remove duplicates while preserving order
    unique_headers = list(dict.fromkeys(all_headers))
    
    # Calculate successful docs count
    successful_docs = len([res for res in individual_results if res["success"] and res["headers"]])
    
    # Process headers in batches for better parallelization
    batch_size = 50  # Process headers in batches of 50
    header_batches = [unique_headers[i:i + batch_size] for i in range(0, len(unique_headers), batch_size)]
    
    async def process_header_batch(batch_headers: List[str], batch_num: int) -> List[str]:
        """Process a batch of headers to identify the most important ones."""
        doc_summary = "\n".join(
            f"Document: {res['filename']}\nHeaders: {', '.join(res['headers'])}\n"
            for res in individual_results if res["success"] and res["headers"]
        )

        batch_prompt = f"""
You are an expert data analyst tasked with identifying the MOST IMPORTANT headers from a batch of headers.

CONTEXT:
- You have been provided headers extracted from {successful_docs} different documents
- You are processing batch {batch_num + 1} of {len(header_batches)} batches
- These headers will be used to extract data from ALL documents in a collection
- The final goal is to select approximately {lattice_count} headers total across all batches

DOCUMENT HEADERS ANALYSIS:
{doc_summary}

BATCH HEADERS TO ANALYZE ({len(batch_headers)} headers):
{', '.join(batch_headers)}

INSTRUCTIONS:
1. **Relevance Assessment**: Identify headers that represent extractable, actionable data
2. **Cross-Document Applicability**: Prefer headers that would be useful across different document types
3. **Specificity Balance**: Choose headers that are specific enough to be meaningful but general enough to be reusable
4. **Data Quality**: Focus on headers that represent concrete, measurable data points
5. **Professional Standards**: Use clear, professional terminology

SELECTION CRITERIA:
- Include headers that would likely have data in at least 25-35% of documents
- Prefer broader terms over very specific ones
- Focus on extractable, comparable data points
- Avoid overly subjective or narrative-based headers
- Select the most important headers from this batch (aim for quality over quantity)

CRITICAL: You must respond with ONLY a valid JSON object in this exact format:
{{
    "selected_headers": ["Header 1", "Header 2", "Header 3", ...],
    "reasoning": "Brief explanation of selection criteria and key decisions"
}}

No other text, explanations, or formatting. Just the JSON.
"""

        try:
            response = await asyncio.to_thread(
                client.chat.completions.create,
                model="agentic-large", 
                messages=[
                    {"role": "system", "content": "You are a data analyst. Respond with valid JSON only."},
                    {"role": "user", "content": batch_prompt}
                ],
                temperature=0.2,
                max_tokens=1000
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Try to extract JSON from response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                result = json.loads(json_text)
            else:
                result = json.loads(response_text)

            if "selected_headers" in result and isinstance(result["selected_headers"], list):
                return result["selected_headers"]
            else:
                return []

        except Exception as e:
            print(f"✗ Error processing header batch {batch_num + 1}: {e}")
            return []

    # Process all batches in parallel
    batch_tasks = [process_header_batch(batch, i) for i, batch in enumerate(header_batches)]
    batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
    
    # Combine results from all batches
    all_selected_headers = []
    for batch_result in batch_results:
        if isinstance(batch_result, list):
            all_selected_headers.extend(batch_result)
    
    # Final consolidation to remove duplicates and limit to specified size
    final_headers = list(dict.fromkeys(all_selected_headers))[:lattice_count]  # Limit to lattice_count headers
    
    if final_headers:
        print(f"✅ Successfully reduced to {len(final_headers)} final headers using parallel batch processing.")
        return {"final_headers": final_headers, "success": True, "error": None}
    else:
        print(f"❌ No headers selected from batch processing")
        return {"final_headers": [], "success": False, "error": "No headers selected from batch processing"}

async def extract_and_consolidate_lattice_headers(pdf_paths: List[str], extraction_count: int = 20, final_count: int = 10) -> List[str]:
    """
    Main workflow for Phase 1: Parse PDFs, extract headers from each, and consolidate them.
    """
    print(f"\n{'='*60}\nPHASE 1: DYNAMIC LATTICE HEADER EXTRACTION\n{'='*60}")

    # Step 1: Parse all documents in parallel
    print(f"📄 Parsing {len(pdf_paths)} documents...")
    parsed_contents = {}
    parse_tasks = [get_or_parse_pdf_content(pdf_path) for pdf_path in pdf_paths]
    results = await asyncio.gather(*parse_tasks, return_exceptions=True)
    
    for i, res in enumerate(results):
        if isinstance(res, Exception):
            print(f"❌ Failed to parse {Path(pdf_paths[i]).name}: {res}")
        else:
            parsed_contents[pdf_paths[i]] = res
    
    if not parsed_contents:
        print("❌ No documents could be parsed. Aborting.")
        return []

    # Step 2: Extract headers from each parsed document in parallel
    print(f"🤖 Extracting headers from {len(parsed_contents)} successfully parsed documents...")
    extract_tasks = [extract_headers_from_document(path, content, extraction_count) for path, content in parsed_contents.items()]
    individual_results = await asyncio.gather(*extract_tasks, return_exceptions=True)

    # Filter out exceptions from header extraction
    successful_extractions = [res for res in individual_results if not isinstance(res, Exception)]
    print(f"📊 Header extraction results: {len(successful_extractions)} successful")

    # Step 3: Reduce headers to a final consolidated set
    if not successful_extractions:
        print("⚠️ No headers were extracted")
        return []

    final_result = await reduce_headers_to_final_set(successful_extractions, final_count)

    if final_result["success"]:
        print(f"🎯 Phase 1 Complete. Generated {len(final_result['final_headers'])} consolidated headers.")
        return final_result["final_headers"]
    else:
        print(f"❌ Phase 1 Failed: {final_result['error']}")
        return []


# HYBRID RAG PROCESSING & QUERYING

def calculate_tokens(text: str) -> int:
    """Estimate token count (4 chars ≈ 1 token)."""
    return len(text) // 4

def chunk_text(text: str, max_tokens: int = 7000) -> List[Dict[str, str]]:
    """Chunk text into segments, assigning a unique ID to each chunk."""
    print(f"Chunking text (~{calculate_tokens(text)} tokens) into max {max_tokens}-token chunks with unique IDs.")
    max_chars = max_tokens * 4
    sentences = text.split('. ')
    chunks, current_chunk_text = [], ""

    for sentence in sentences:
        sentence_with_period = sentence + '. '
        if len(current_chunk_text) + len(sentence_with_period) > max_chars:
            if current_chunk_text:
                chunks.append({
                    "chunk_id": f"chunk-{uuid.uuid4()}",
                    "text": current_chunk_text.strip()
                })
            current_chunk_text = sentence_with_period
        else:
            current_chunk_text += sentence_with_period

    if current_chunk_text.strip():
        chunks.append({
            "chunk_id": f"chunk-{uuid.uuid4()}",
            "text": current_chunk_text.strip()
        })

    print(f"Created {len(chunks)} chunks.")
    return chunks

def get_jina_embeddings(texts: List[str], batch_size: int = 50, max_retries: int = 3) -> np.ndarray:
    """Fetch embeddings from Jina API with batching and retry logic."""
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {JINA_API_KEY}"}
    all_embeddings = []
    
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i + batch_size]
        for attempt in range(max_retries):
            try:
                data = {"model": "jina-embeddings-v2-base-en", "input": batch_texts}
                response = requests.post(JINA_API_URL, headers=headers, json=data, timeout=60)
                response.raise_for_status()
                batch_embeddings = response.json()["data"]
                all_embeddings.extend([e["embedding"] for e in batch_embeddings])
                break
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    raise
    
    return np.array(all_embeddings).astype("float32")

def get_embedding_dimension() -> int:
    """Get the actual embedding dimension from JINA API."""
    try:
        # Test with a single text to get the embedding dimension
        test_embedding = get_jina_embeddings(["test"])
        return test_embedding.shape[1]
    except Exception as e:
        print(f"Warning: Could not determine embedding dimension, using default 1024: {e}")
        return 1024

def build_dense_index(chunks: List[Dict[str, str]], document_name: str = None) -> QdrantClient:
    """Build a Qdrant index from document chunks using Jina embeddings."""
    print("Building dense (Qdrant) index...")
    collection_name = QDRANT_COLLECTION_NAME
    
    # Get the actual embedding dimension
    embedding_dim = get_embedding_dimension()
    print(f"Using embedding dimension: {embedding_dim}")
    
    # Check if collection exists, if not create it
    collections = qdrant_client.get_collections()
    collection_names = [col.name for col in collections.collections]
    
    if collection_name in collection_names:
        # Check if the existing collection has the correct dimension
        try:
            collection_info = qdrant_client.get_collection(collection_name)
            existing_dim = collection_info.config.params.vectors.size
            if existing_dim != embedding_dim:
                print(f"⚠️ Collection '{collection_name}' exists with dimension {existing_dim}, but need {embedding_dim}")
                print(f"🗑️ Deleting existing collection and recreating...")
                qdrant_client.delete_collection(collection_name=collection_name)
                qdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(size=embedding_dim, distance=Distance.COSINE),
                    optimizers_config={"default_segment_number": 2}
                )
                # Create payload index for document_name
                qdrant_client.create_payload_index(
                    collection_name=collection_name,
                    field_name="document_name",
                    field_schema="keyword"
                )
                print(f"✅ Collection '{collection_name}' recreated with dimension {embedding_dim} and document_name index")
            else:
                print(f"Collection '{collection_name}' already exists with correct dimension {embedding_dim}")
                # Check if document_name index exists, if not create it
                try:
                    qdrant_client.get_collection(collection_name)
                    # Try to create index if it doesn't exist
                    qdrant_client.create_payload_index(
                        collection_name=collection_name,
                        field_name="document_name",
                        field_schema="keyword"
                    )
                    print(f"✅ Added document_name index to existing collection")
                except Exception as e:
                    print(f"ℹ️ Document name index already exists or error: {e}")
        except Exception as e:
            print(f"⚠️ Error checking collection dimension: {e}")
            # Delete and recreate if there's an error
            try:
                qdrant_client.delete_collection(collection_name=collection_name)
            except:
                pass
            qdrant_client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(size=embedding_dim, distance=Distance.COSINE),
                optimizers_config={"default_segment_number": 2}
            )
            # Create payload index for document_name
            qdrant_client.create_payload_index(
                collection_name=collection_name,
                field_name="document_name",
                field_schema="keyword"
            )
            print(f"✅ Collection '{collection_name}' created with dimension {embedding_dim} and document_name index")
    else:
        print(f"Creating new collection: {collection_name}")
        qdrant_client.create_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(size=embedding_dim, distance=Distance.COSINE),
            optimizers_config={"default_segment_number": 2}
        )
        # Create payload index for document_name
        qdrant_client.create_payload_index(
            collection_name=collection_name,
            field_name="document_name",
            field_schema="keyword"
        )
        print(f"✅ Collection '{collection_name}' created with document_name index")

    chunk_texts = [chunk['text'] for chunk in chunks]
    embeddings = get_jina_embeddings(chunk_texts)
    
    # Create points for Qdrant with UUIDs and document metadata
    points = []
    uuid_to_index = {}  # Mapping UUID to chunk index
    for i, chunk in enumerate(chunks):
        chunk_uuid = str(uuid.uuid4())
        uuid_to_index[chunk_uuid] = i
        
        # Create payload with document metadata
        payload = {
            "text": chunk['text'], 
            "chunk_id": chunk['chunk_id'], 
            "index": i
        }
        
        # Add document name to payload if provided
        if document_name:
            payload["document_name"] = document_name
        
        points.append(PointStruct(
            id=chunk_uuid,
            vector=embeddings[i].tolist(),  # Convert numpy array to list
            payload=payload
        ))
    
    # Upsert points to Qdrant with retry logic
    max_retries = 3
    for attempt in range(max_retries):
        try:
            qdrant_client.upsert(collection_name=collection_name, points=points)
            print(f"✓ Dense index built with {len(points)} vectors in collection '{collection_name}'.")
            break
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"⚠️ Qdrant upsert attempt {attempt + 1} failed: {e}. Retrying in {2 ** attempt} seconds...")
                time.sleep(2 ** attempt)
            else:
                print(f"❌ Qdrant upsert failed after {max_retries} attempts: {e}")
                raise
    
    # Store the UUID mapping and document name in the client for later use
    qdrant_client.uuid_to_index = uuid_to_index
    qdrant_client.document_name = document_name
    return qdrant_client

# FAISS version (commented out for potential future use)
# def build_dense_index_faiss(chunks: List[Dict[str, str]]) -> faiss.IndexIDMap:
#     """Build a FAISS index from document chunks using Jina embeddings."""
#     print("Building dense (FAISS) index...")
#     chunk_texts = [chunk['text'] for chunk in chunks]
#     embeddings = get_jina_embeddings(chunk_texts)
#     dim = embeddings.shape[1]
#     index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))
#     index.add_with_ids(embeddings, np.arange(len(chunks)))
#     print(f"✓ Dense index built with {index.ntotal} vectors.")
#     return index

def build_sparse_index(chunks: List[Dict[str, str]]) -> BM25Okapi:
    """Build a BM25 sparse index from document chunks."""
    print("Building sparse (BM25) index...")
    chunk_texts = [chunk['text'] for chunk in chunks]
    tokenized_chunks = [word_tokenize(chunk.lower()) for chunk in chunk_texts]
    bm25 = BM25Okapi(tokenized_chunks)
    print(f"✓ Sparse index built for {len(tokenized_chunks)} documents.")
    return bm25

class DocumentProcessor:
    """A class to hold the processed state of a single document."""
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.document_info = {"file_name": Path(pdf_path).name, "file_path": pdf_path}
        self.content = None
        self.chunks: List[Dict[str, str]] = [] 
        self.qdrant_client: QdrantClient = None  # Changed from faiss_index
        self.bm25_index = None
        self.uuid_to_index: Dict[str, int] = {}  # Mapping UUID to chunk index

    async def process(self):
        """Full processing pipeline for a single document."""
        print(f"\n🔄 Processing document: {self.document_info['file_name']}")

        try:
            # Get content first
            self.content = await get_or_parse_pdf_content(self.pdf_path)
            self.chunks = chunk_text(self.content)

            if self.chunks:
                # Build sparse index first (no semaphore needed)
                sparse_task = asyncio.to_thread(build_sparse_index, self.chunks)

                # Build dense index with semaphore to limit concurrent Qdrant operations
                async def build_dense_with_semaphore():
                    async with qdrant_semaphore:
                        return await asyncio.to_thread(build_dense_index, self.chunks, self.document_info['file_name'])

                dense_task = build_dense_with_semaphore()

                # Wait for both indexing tasks to complete
                self.qdrant_client, self.bm25_index = await asyncio.gather(dense_task, sparse_task)

                print(f"✅ Document processed: {self.document_info['file_name']}")
            else:
                print(f"⚠️ No chunks created for {self.document_info['file_name']}. Skipping indexing.")
            return self
        except Exception as e:
            print(f"❌ Error processing document {self.document_info['file_name']}: {e}")
            import traceback
            print(f"  Full traceback: {traceback.format_exc()}")
            raise

def hybrid_query(query: str, processor: DocumentProcessor, top_k: int = 5, rrf: bool = True) -> List[Dict[str, str]]:
    """Perform a hybrid search and return the full retrieved chunk objects."""
    try:
        if not processor.chunks:
            print(f"  ⚠️ No chunks available for query: {query}")
            return []
            
        # Sparse search
        tokenized_query = word_tokenize(query.lower())
        bm25_scores = processor.bm25_index.get_scores(tokenized_query)
        bm25_top_ids = np.argsort(bm25_scores)[::-1][:top_k]
        
        # Dense search using Qdrant with document name pre-filtering
        query_vec = get_jina_embeddings([query])
        
        # Create filter for document name if available
        search_filter = None
        if processor.qdrant_client.document_name:
            search_filter = Filter(
                must=[
                    FieldCondition(
                        key="document_name",
                        match=MatchValue(value=processor.qdrant_client.document_name)
                    )
                ]
            )
        
        search_result = processor.qdrant_client.query_points(
            collection_name=QDRANT_COLLECTION_NAME, # Use the shared collection name
            query=query_vec[0].tolist(),  # Convert numpy array to list
            limit=top_k,
            query_filter=search_filter  # Add document name filter
        )
        # Convert UUIDs back to indices using the mapping
        qdrant_top_ids = [processor.qdrant_client.uuid_to_index.get(point.id, -1) for point in search_result.points]
        qdrant_top_ids = [idx for idx in qdrant_top_ids if idx >= 0]  # Filter out invalid indices

        # Reciprocal Rank Fusion (RRF)
        ranks = {idx: 0 for idx in range(len(processor.chunks))}
        
        # Filter BM25 indices to ensure they're within bounds
        valid_bm25_ids = [idx for idx in bm25_top_ids if 0 <= idx < len(processor.chunks)]
        for rank, idx in enumerate(valid_bm25_ids):
            ranks[idx] += 1 / (rank + 60)
            
        # Filter Qdrant indices to ensure they're within bounds
        valid_qdrant_ids = [idx for idx in qdrant_top_ids if 0 <= idx < len(processor.chunks)]
        for rank, idx in enumerate(valid_qdrant_ids):
            ranks[idx] += 1 / (rank + 60)
            
        combined_ids = sorted(ranks.keys(), key=lambda x: ranks[x], reverse=True)[:top_k]

        return [processor.chunks[idx] for idx in combined_ids]
    except Exception as e:
        print(f"  ❌ Error in hybrid_query for '{query}': {e}")
        import traceback
        print(f"  Full traceback: {traceback.format_exc()}")
        return []

# FAISS version (commented out for potential future use)
# def hybrid_query_faiss(query: str, processor: DocumentProcessor, top_k: int = 5, rrf: bool = True) -> List[Dict[str, str]]:
#     """Perform a hybrid search using FAISS and return the full retrieved chunk objects."""
#     if not processor.chunks:
#         return []
#         
#     # Sparse search
#     tokenized_query = word_tokenize(query.lower())
#     bm25_scores = processor.bm25_index.get_scores(tokenized_query)
#     bm25_top_ids = np.argsort(bm25_scores)[::-1][:top_k]
#     
#     # Dense search using FAISS
#     query_vec = get_jina_embeddings([query])
#     _, faiss_top_ids = processor.faiss_index.search(query_vec, top_k)
#     faiss_top_ids = faiss_top_ids[0]
#     
#     # Filter out -1 indices from FAISS (no matches)
#     faiss_top_ids = faiss_top_ids[faiss_top_ids >= 0]
# 
#     # Reciprocal Rank Fusion (RRF)
#     ranks = {idx: 0 for idx in range(len(processor.chunks))}
#     for rank, idx in enumerate(bm25_top_ids):
#         ranks[idx] += 1 / (rank + 60)
#     for rank, idx in enumerate(faiss_top_ids):
#         ranks[idx] += 1 / (rank + 60)
#         
#     combined_ids = sorted(ranks.keys(), key=lambda x: ranks[x], reverse=True)[:top_k]
# 
#     return [processor.chunks[idx] for idx in combined_ids]

def generate_structured_answer(query: str, retrieved_chunks: List[Dict[str, str]], model: str = "agentic-large") -> Dict[str, Any]:
    """
    Generate a structured JSON answer from an LLM, including answer, confidence, and citations.
    """
    try:
        print(f"  Generating structured answer for: '{query}'")
        
        if not retrieved_chunks:
            return {
                "answer": "No relevant information found in the document.",
                "confidence_score": 0.0,
                "citations": []
            }
        
        # Create a context string
        context = "\n\n".join(
            f"--- Chunk ID: {chunk['chunk_id']} ---\n{chunk['text']}"
            for chunk in retrieved_chunks
        )

        prompt = f"""
Based on the provided context, answer the question about: {query}

CONTEXT:
{context}

Instructions:
1. Provide a concise, accurate answer based ONLY on the context
2. If the information is NOT in the context, respond with "Information not available in the provided context" and set confidence_score to 0.0
3. If you find relevant information, include a confidence score (0.0 to 1.0) based on how certain you are
4. Only list chunk IDs as citations if you actually used that chunk's information in your answer
5. If no relevant information is found, set confidence_score to 0.0 and citations to empty array []

CRITICAL RULES:
- If you cannot find specific information about {query} in the context, your answer must be "Information not available in the provided context"
- If you say "Information not available", your confidence_score MUST be 0.0 and citations MUST be []
- Only provide citations for chunks that actually contain relevant information about {query}
- Do not make up information or cite chunks that don't contain relevant data
- If the context mentions the topic but doesn't provide the specific information requested, say "Information not available in the provided context"
- If you're unsure or the information is incomplete, set confidence_score to 0.0

Respond with ONLY this JSON format:
{{
    "answer": "Your answer here",
    "confidence_score": 0.85,
    "citations": ["chunk-id-1", "chunk-id-2"]
}}
"""
        
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are an AI assistant that provides structured answers in JSON format."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=2000
        )
        
        response_text = response.choices[0].message.content.strip()
        
        # Try to extract JSON from response
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1
        
        if json_start != -1 and json_end != -1:
            json_text = response_text[json_start:json_end]
            result = json.loads(json_text)
        else:
            result = json.loads(response_text)
        
        # Validate the result structure
        if not isinstance(result, dict):
            raise ValueError("Invalid response structure")
        
        if "answer" not in result or "confidence_score" not in result or "citations" not in result:
            raise ValueError("Missing required fields in response")
        
        return result
        
    except json.JSONDecodeError as e:
        print(f"  ✗ JSON decode error for query '{query}': {e}")
        return {
            "answer": f"Error parsing response: {str(e)}",
            "confidence_score": 0.0,
            "citations": []
        }
    except Exception as e:
        print(f"  ✗ Error generating structured answer for '{query}': {e}")
        import traceback
        print(f"  Full traceback: {traceback.format_exc()}")
        return {
            "answer": f"An error occurred while generating the answer: {e}",
            "confidence_score": 0.0,
            "citations": []
        }

async def run_parallel_lattice_analysis(
    pdf_paths: List[str],
    lattice_headers: List[str],
    top_k: int = 5,
    rrf: bool = True
) -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Main pipeline for Phase 2: Process documents and query them in parallel with lattice headers.
    """
    print(f"\n{'='*60}\nPHASE 2: PARALLEL LATTICE ANALYSIS\n{'='*60}")
    
    # Step 1: Process all documents in parallel
    doc_processors = [DocumentProcessor(path) for path in pdf_paths]
    process_tasks = [proc.process() for proc in doc_processors]
    processed_docs = await asyncio.gather(*process_tasks, return_exceptions=True)
    
    successful_processors = [p for p in processed_docs if isinstance(p, DocumentProcessor) and p.chunks]
    if not successful_processors:
        print("❌ No documents were successfully processed for RAG analysis. Aborting.")
        return {}

    # Step 2: Query all documents with all headers in parallel
    print(f"\n🔍 Querying {len(successful_processors)} documents with {len(lattice_headers)} headers...")
    final_results = {}

    async def query_and_generate(processor: DocumentProcessor, header: str):
        try:
            print(f"  Executing hybrid query for: '{header}' on {processor.document_info['file_name']}")
            
            # Execute hybrid query and structured answer generation concurrently
            hybrid_task = asyncio.to_thread(hybrid_query, header, processor, top_k, rrf)
            
            # Start the hybrid query first
            retrieved_chunks = await hybrid_task
            
            if not retrieved_chunks:
                print(f"⚠️  No chunks retrieved for {processor.document_info['file_name']} - {header}")
                return processor.document_info['file_name'], header, {
                    "answer": "No relevant information found in the document.",
                    "confidence_score": 0.0,
                    "citations": []
                }
            
            # Generate structured answer
            structured_answer = await asyncio.to_thread(generate_structured_answer, header, retrieved_chunks)
            return processor.document_info['file_name'], header, structured_answer
        except Exception as e:
            print(f"❌ Error processing {processor.document_info['file_name']} - {header}: {e}")
            import traceback
            print(f"  Full traceback: {traceback.format_exc()}")
            return processor.document_info['file_name'], header, {
                "answer": f"Error occurred while processing: {str(e)}",
                "confidence_score": 0.0,
                "citations": []
            }

    all_tasks = []
    task_combinations = [(processor, header) for processor in successful_processors for header in lattice_headers]
    import random
    random.shuffle(task_combinations)
    
    for processor, header in task_combinations:
        all_tasks.append(query_and_generate(processor, header))
    
    all_results = await asyncio.gather(*all_tasks, return_exceptions=True)
    
    # Track success/failure statistics
    successful_queries = 0
    failed_queries = 0
    documents_with_results = set()
    
    # Organize results by document
    for res in all_results:
        if isinstance(res, Exception):
            failed_queries += 1
            print(f"❌ Query failed with exception: {res}")
            continue
            
        doc_name, header, answer_obj = res
        documents_with_results.add(doc_name)
        
        if doc_name not in final_results:
            final_results[doc_name] = {}
        
        if answer_obj and isinstance(answer_obj, dict):
            final_results[doc_name][header] = answer_obj
            successful_queries += 1
        else:
            failed_queries += 1
            print(f"⚠️  Invalid answer object for {doc_name} - {header}")
            
    print(f"\n📊 Query Statistics:")
    print(f"  ✅ Successful queries: {successful_queries}")
    print(f"  ❌ Failed queries: {failed_queries}")
    print(f"  📄 Documents with results: {len(documents_with_results)}")
    print(f"  📄 Total documents processed: {len(successful_processors)}")
    
    for doc_name in [p.document_info['file_name'] for p in successful_processors]:
        if doc_name not in documents_with_results:
            print(f"⚠️  No results generated for document: {doc_name}")
    
    print(f"\n✅ Phase 2 Complete. Analysis finished for {len(successful_processors)} documents.")
    return final_results


# RESULTS PROCESSING & MAIN EXECUTION 

def save_results_to_file(results: Dict[str, Dict[str, Dict[str, Any]]], output_path: str = "lattice_results.txt"):
    """Save the final analysis to a formatted text file, including confidence and citations."""
    result_string = "LATTICE ANALYSIS RESULTS\n" + "=" * 60 + "\n\n"
    for doc_name, headers_data in results.items():
        result_string += f"📄 DOCUMENT: {doc_name}\n" + "-" * 40 + "\n"
        for header, data in headers_data.items():
            answer = data.get('answer', 'N/A')
            confidence = data.get('confidence_score', 0.0)
            citations = data.get('citations', [])
            
            result_string += f"🎯 {header}:\n"
            result_string += f"   - Answer: {answer}\n"
            result_string += f"   - Confidence: {confidence:.2%}\n"
            result_string += f"   - Citations: {', '.join(citations) if citations else 'None'}\n\n"
        result_string += "\n"

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(result_string)
    print(f"\n📊 Results saved to: {output_path}")

async def main():
    """Main execution entry point for the entire pipeline."""
    print("🚀 STARTING HYBRID RAG & LATTICE ANALYSIS PIPELINE 🚀")
    
    pdf_paths = [
        "../../resido/pdfs/form-10-k-3.pdf",
        "../../mp_materials/pdfs/form-10-q.pdf",
        "../../mp_materials/pdfs/form-10-q-2.pdf",
    ]
    
    valid_paths = [p for p in pdf_paths if Path(p).exists()]
    if len(valid_paths) != len(pdf_paths):
        print("⚠️  Warning: Some PDF paths were not found and will be skipped.")
    
    if not valid_paths:
        print("❌ Error: No valid PDF files found. Exiting.")
        return

    try:
        #  Extract and consolidate headers
        print("\n🎯 Starting Phase 1: Header Extraction and Consolidation")
        lattice_headers = await extract_and_consolidate_lattice_headers(valid_paths)
        if not lattice_headers:
            print("❌ Pipeline halted because no lattice headers could be generated.")
            return

        print("\n🎯 FINAL CONSOLIDATED LATTICE HEADERS TO BE USED FOR ANALYSIS:")
        for i, header in enumerate(lattice_headers, 1):
            print(f"  {i:2d}. {header}")

        #  Parallel lattice analysis
        print("\n🎯 Starting Phase 2: Parallel Lattice Analysis")
        results = await run_parallel_lattice_analysis(
            pdf_paths=valid_paths,
            lattice_headers=lattice_headers,
            top_k=5,
            rrf=True
        )

        if results:
            save_results_to_file(results)
            print("\n🎉 Analysis completed successfully!")
        else:
            print("\n⚠️  Analysis finished, but no results were generated.")

    except Exception as e:
        print(f"\n❌ An unexpected error occurred in the main pipeline: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
