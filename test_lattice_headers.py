#!/usr/bin/env python3
"""
Test script for <PERSON><PERSON><PERSON> Extraction in Hotfix Upload API

This script tests both paths:
1. Path A: Providing headers (fast response)
2. Path B: Extracting headers from documents (parallel processing)
"""

import requests
import time
import json
from typing import Dict, Any, List, Optional

# API Configuration
API_BASE = "http://localhost:8001"

def print_section(title: str):
    """Print a formatted section header"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_subsection(title: str):
    """Print a formatted subsection header"""
    print(f"\n{'-'*40}")
    print(f"📋 {title}")
    print(f"{'-'*40}")

def test_available_files() -> List[str]:
    """Get list of available files and return some for testing"""
    print_subsection("Getting Available Files")
    
    response = requests.get(f"{API_BASE}/available-files")
    
    if response.status_code == 200:
        data = response.json()
        files = [f["filename"] for f in data["files"]]
        print(f"✅ Found {len(files)} available files")
        
        # Use first 3 files for testing
        test_files = files[:3]
        print(f"🎯 Selected for testing: {test_files}")
        return test_files
    else:
        print(f"❌ Failed to get available files: {response.status_code}")
        return []

def start_upload_job(filenames: List[str]) -> tuple[Optional[str], Optional[str]]:
    """Start upload job and return job_id and session_id"""
    print_subsection("Starting Upload Job")
    
    payload = {"filenames": filenames}
    response = requests.post(f"{API_BASE}/upload", json=payload)
    
    if response.status_code == 200:
        data = response.json()
        job_id = data['job_id']
        session_id = data['session_id']
        
        print(f"✅ Upload job started")
        print(f"   🆔 Job ID: {job_id}")
        print(f"   🎫 Session ID: {session_id}")
        print(f"   📊 Status: {data['status']}")
        print(f"   📝 Message: {data['message']}")
        
        return job_id, session_id
    else:
        print(f"❌ Failed to start upload: {response.status_code}")
        print(f"   Error: {response.text}")
        return None, None

def test_path_a_provided_headers(session_id: str) -> Dict[str, Any]:
    """Test Path A: Process docs with provided headers"""
    print_section("PATH A: Testing with Provided Headers")
    
    provided_headers = [
        "Revenue",
        "EBITDA", 
        "Operating Cash Flow",
        "Working Capital",
        "Total Assets"
    ]
    
    print(f"🏷️ Providing {len(provided_headers)} headers:")
    for i, header in enumerate(provided_headers, 1):
        print(f"   {i}. {header}")
    
    start_time = time.time()
    
    payload = {
        "session_id": session_id,
        "lattice_headers": provided_headers
    }
    
    print(f"\n🚀 Calling /process_docs with provided headers...")
    response = requests.post(f"{API_BASE}/process_docs", json=payload)
    
    response_time = time.time() - start_time
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"✅ Path A completed successfully!")
        print(f"⚡ Response time: {response_time:.3f}s (should be very fast)")
        print(f"🎫 Session ID: {data['session_id']}")
        print(f"📊 Status: {data['status']}")
        print(f"📁 Total files: {data['total_files']}")
        print(f"🏷️ Header source: {data['header_source']}")
        print(f"⏱️ Header extraction time: {data['header_extraction_time']}s")
        print(f"📋 Returned headers ({len(data['lattice_headers'])}):")
        
        for i, header in enumerate(data['lattice_headers'], 1):
            print(f"   {i}. {header}")
        
        # Show real lattice results for Path A too
        if data.get('lattice_results'):
            print(f"🔍 Real Lattice Results:")
            for header, doc_results in data['lattice_results'].items():
                if isinstance(doc_results, dict):
                    doc_count = len(doc_results)
                    print(f"   🏷️ {header}: {doc_count} documents")
                    for doc_name, result in doc_results.items():
                        confidence = result.get('confidence_score', 0.0)
                        answer_preview = result.get('answer', '')[:60] + '...' if result.get('answer', '') else 'No answer'
                        print(f"      📄 {doc_name}: {confidence:.2f} - {answer_preview}")
                else:
                    result_count = len(doc_results) if isinstance(doc_results, (list, dict)) else 0
                    print(f"   🏷️ {header}: {result_count} results")
        
        print(f"⏱️ Performance breakdown:")
        print(f"   📊 Header extraction: {data.get('header_extraction_time', 0):.2f}s") 
        print(f"   🔍 Lattice retrieval: {data.get('retrieval_time', 0):.2f}s")
        print(f"   🕒 Total processing: {data.get('total_processing_time', 0):.2f}s")
        
        print(f"📝 Message: {data['message']}")
        
        # Verify headers match what we provided
        if data['lattice_headers'] == provided_headers:
            print(f"✅ Headers match exactly what was provided")
        else:
            print(f"❌ Headers don't match! Expected: {provided_headers}")
            
        return data
    else:
        print(f"❌ Path A failed: {response.status_code}")
        print(f"   Error: {response.text}")
        return {}

def test_path_b_extract_headers(session_id: str) -> Dict[str, Any]:
    """Test Path B: Process docs and extract headers"""
    print_section("PATH B: Testing Header Extraction")
    
    print(f"🧠 No headers provided - will extract from documents")
    print(f"🔄 This will trigger parallel processing of all documents")
    
    start_time = time.time()
    
    payload = {
        "session_id": session_id
        # Note: no lattice_headers provided
    }
    
    print(f"\n🚀 Calling /process_docs without headers...")
    print(f"⏳ This may take a while as documents are processed...")
    
    response = requests.post(f"{API_BASE}/process_docs", json=payload)
    
    response_time = time.time() - start_time
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"✅ Path B completed successfully!")
        print(f"⏱️ Total response time: {response_time:.2f}s")
        print(f"🎫 Session ID: {data['session_id']}")
        print(f"📊 Status: {data['status']}")
        print(f"📁 Total files: {data['total_files']}")
        print(f"🏷️ Header source: {data['header_source']}")
        print(f"⚗️ Header extraction time: {data['header_extraction_time']:.2f}s")
        
        if data['lattice_headers']:
            print(f"🎯 Extracted headers ({len(data['lattice_headers'])}):")
            for i, header in enumerate(data['lattice_headers'], 1):
                print(f"   {i}. {header}")
        else:
            print(f"⚠️ No headers extracted (may have failed)")
        
        # Show real lattice results structure
        if data.get('lattice_results'):
            print(f"🔍 Real Lattice Results:")
            for header, doc_results in data['lattice_results'].items():
                if isinstance(doc_results, dict):
                    # Real structure: {header: {doc_name: {answer, confidence_score, etc.}}}
                    doc_count = len(doc_results)
                    print(f"   🏷️ {header}: {doc_count} documents")
                    for doc_name, result in doc_results.items():
                        confidence = result.get('confidence_score', 0.0)
                        chunks = result.get('chunks_retrieved', 0)
                        answer_preview = result.get('answer', '')[:100] + '...' if result.get('answer', '') else 'No answer'
                        print(f"      📄 {doc_name}")
                        print(f"         🎯 Confidence: {confidence:.2f}")
                        print(f"         📊 Chunks: {chunks}")
                        print(f"         💬 Answer: {answer_preview}")
                else:
                    result_count = len(doc_results) if isinstance(doc_results, (list, dict)) else 0
                    print(f"   🏷️ {header}: {result_count} results (legacy format)")
        
        print(f"⏱️ Performance breakdown:")
        print(f"   📊 Header extraction: {data.get('header_extraction_time', 0):.2f}s") 
        print(f"   🔍 Lattice retrieval: {data.get('retrieval_time', 0):.2f}s")
        print(f"   🕒 Total processing: {data.get('total_processing_time', 0):.2f}s")
            
        print(f"📝 Message: {data['message']}")
        
        # Performance analysis
        if data.get('total_processing_time', 0) > 0:
            files_per_sec = data['total_files'] / data['total_processing_time']
            print(f"📈 Overall performance: {files_per_sec:.2f} files/second")
        
        return data
    else:
        print(f"❌ Path B failed: {response.status_code}")
        print(f"   Error: {response.text}")
        return {}

def compare_paths(path_a_data: Dict[str, Any], path_b_data: Dict[str, Any]):
    """Compare the results from both paths"""
    print_section("PATH COMPARISON")
    
    if not path_a_data or not path_b_data:
        print(f"❌ Cannot compare - one or both paths failed")
        return
    
    print(f"📊 Comparison Results:")
    print(f"   Path A (Provided) - Response time: {path_a_data.get('header_extraction_time', 0):.3f}s")
    print(f"   Path B (Extracted) - Response time: {path_b_data.get('header_extraction_time', 0):.2f}s")
    
    path_a_headers = set(path_a_data.get('lattice_headers', []))
    path_b_headers = set(path_b_data.get('lattice_headers', []))
    
    print(f"\n🏷️ Header Analysis:")
    print(f"   Path A headers: {len(path_a_headers)}")
    print(f"   Path B headers: {len(path_b_headers)}")
    
    if path_a_headers and path_b_headers:
        common_headers = path_a_headers.intersection(path_b_headers)
        unique_a = path_a_headers - path_b_headers
        unique_b = path_b_headers - path_a_headers
        
        print(f"   Common headers: {len(common_headers)}")
        if common_headers:
            print(f"      {list(common_headers)}")
            
        print(f"   Unique to provided: {len(unique_a)}")
        if unique_a:
            print(f"      {list(unique_a)}")
            
        print(f"   Unique to extracted: {len(unique_b)}")
        if unique_b:
            print(f"      {list(unique_b)}")
    
    # Speed comparison
    if path_a_data.get('header_extraction_time', 0) > 0 and path_b_data.get('header_extraction_time', 0) > 0:
        speedup = path_b_data['header_extraction_time'] / path_a_data['header_extraction_time']
        print(f"\n⚡ Speed Analysis:")
        print(f"   Path A is {speedup:.0f}x faster than Path B (as expected)")

def test_edge_cases(session_id: str):
    """Test edge cases and error handling"""
    print_section("EDGE CASE TESTING")
    
    # Test 1: Invalid session ID
    print_subsection("Test 1: Invalid Session ID")
    payload = {"session_id": "invalid_session_123"}
    response = requests.post(f"{API_BASE}/process_docs", json=payload)
    
    if response.status_code == 404:
        print(f"✅ Correctly handled invalid session ID (404)")
    else:
        print(f"❌ Unexpected response for invalid session: {response.status_code}")
    
    # Test 2: Empty headers list
    print_subsection("Test 2: Empty Headers List")
    payload = {
        "session_id": session_id,
        "lattice_headers": []
    }
    response = requests.post(f"{API_BASE}/process_docs", json=payload)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Handled empty headers list")
        print(f"   Header source: {data['header_source']}")
        print(f"   Headers returned: {len(data['lattice_headers'])}")
    else:
        print(f"❌ Failed to handle empty headers: {response.status_code}")
    
    # Test 3: Very long headers list
    print_subsection("Test 3: Long Headers List")
    long_headers = [f"Header_{i}" for i in range(20)]
    payload = {
        "session_id": session_id,
        "lattice_headers": long_headers
    }
    response = requests.post(f"{API_BASE}/process_docs", json=payload)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Handled long headers list ({len(long_headers)} headers)")
        print(f"   Returned: {len(data['lattice_headers'])} headers")
    else:
        print(f"❌ Failed to handle long headers: {response.status_code}")

def main():
    """Main test function"""
    print_section("LATTICE HEADER EXTRACTION TESTING")
    print(f"🎯 Testing both extraction paths with detailed logging")
    print(f"🌐 API Base: {API_BASE}")
    
    try:
        # Step 1: Get available files
        test_files = test_available_files()
        if not test_files:
            print(f"❌ No test files available - cannot proceed")
            return
        
        # Step 2: Start upload job to get session
        job_id, session_id = start_upload_job(test_files)
        if not session_id:
            print(f"❌ Failed to get session ID - cannot proceed")
            return
        
        # Wait a moment for the session to be properly set up
        print(f"\n⏳ Waiting 2 seconds for session setup...")
        time.sleep(2)
        
        # Step 3: Test Path A - Provided headers
        path_a_data = test_path_a_provided_headers(session_id)
        
        # Step 4: Test Path B - Extract headers
        path_b_data = test_path_b_extract_headers(session_id)
        
        # Step 5: Compare results
        compare_paths(path_a_data, path_b_data)
        
        # Step 6: Test edge cases
        test_edge_cases(session_id)
        
        # Final summary
        print_section("TEST SUMMARY")
        print(f"✅ All tests completed!")
        print(f"🎯 Tested both header processing paths")
        print(f"📊 Verified response times and data integrity")
        print(f"🧪 Tested edge cases and error handling")
        print(f"🎫 Session ID used: {session_id}")
        
    except KeyboardInterrupt:
        print(f"\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()